<template>
  <div
    v-loading="loading"
    class="main-content"
  >
    <el-tabs
      v-model="page"
      @tab-click="tabChange()"
    >
      <el-tab-pane
        label="实时趋势"
        name="first"
      />
      <el-tab-pane
        label="单点曲线"
        name="second"
      />
    </el-tabs>
    <div class="top">
      <el-date-picker
        v-if="page === 'second'"
        v-model="date"
        style="margin-right:20px;width:200px;"
        placeholder="选择日期"
        value-format="yyyy-MM-dd"
        :format="'yyyy-MM-dd'"
        append-to-body
        @change="dateChange"
      />
      <!-- <el-select
        v-if="page === 'second'"
        v-model="dataType"
        placeholder="请选择"
        filterable
        allow-create
        style="width:120px;margin:0 10px"
        @change="dataTypeChange"
      >
        <el-option
          v-for="(label, value) in dataTypeMap"
          :key="value"
          :label="label"
          :value="Number(value)"
        />
      </el-select> -->
      <el-cascader
        v-if="page === 'first' && dataType === 1"
        v-model="opticalFiberList"
        style="margin-right:20px;width:400px;"
        :options="sensorList"
        collapse-tags
        clearable
        :props="{ value: 'id', label: 'name', children: 'childList',multiple: true }"
        @change="deviceCascaderChange()"
      />
      <!-- <el-select
        v-if="page === 'first' && dataType === 2"
        v-model="sensor"
        placeholder="请选择"
        filterable
        allow-create
        style="margin-right:20px;width:400px;"
        @change="sensorChange"
      >
        <el-option
          v-for="item in sensorList"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select> -->
      <el-cascader
        v-show="page === 'second' && dataType === 2"
        v-model="idArray"
        style="margin-right:20px;width:300px;"
        :options="sensorList"
        :props="{ value: 'id', label: 'name', children: 'childList' }"
        @change="cascaderChange"
      ></el-cascader>

      <el-cascader
        v-if="page === 'second' && dataType === 1"
        v-model="idArray"
        style="margin-right:20px;width:330px;"
        :options="sensorList"
        :props="{ value: 'id', label: 'name', children: 'childList' }"
        @change="cascaderChange"
      ></el-cascader>
      <!--      <el-select-->
      <!--        v-if="page === 'second' && dataType === 1"-->
      <!--        v-model="monitoringPointId"-->
      <!--        placeholder="请选择"-->
      <!--        filterable-->
      <!--        style="width:100px;margin:0 10px"-->
      <!--        @change="monitoringPointChange"-->
      <!--      >-->
      <!--        <el-option-->
      <!--          v-for="item in monitoringPointList"-->
      <!--          :key="item.id"-->
      <!--          :label="item.name"-->
      <!--          :value="item.id"-->
      <!--        />-->
      <!--      </el-select>-->
      <VirtualSelect
        v-if="page === 'second' && dataType === 1"
        v-model="monitoringPointId"
        placeholder="请选择"
        :list="monitoringPointList"
        label="name"
        value="id"
        filterable
        style="width:100px;margin:0 10px"
        @change="monitoringPointChange"
      />
      <div>

        <!-- <el-cascader
          v-if="page === 'second' && dataType === 2"
          v-model="pointId"
          style="margin-right:20px;width:400px;"
          :options="sensorList"
          :props="{ value: 'id', label: 'name', children: 'points', emitPath: false }"
          @change="cascaderChange"
        /> -->
      </div>

      <el-radio-group
        v-if="page === 'second' && false"
        v-model="dateType"
        size="mini"
        style="margin-right: 20px;"
        @change="dateTypeChange"
      >
        <el-radio-button
          v-for="item in dateTypeList"
          :key="item.value"
          :label="item.value"
        >{{ item.label }}</el-radio-button>
      </el-radio-group>

      <el-date-picker
        v-if="showPicker && false"
        v-model="date"
        :type="dateTypeMap[dateType]"
        style="margin-right:20px;width:200px;"
        placeholder="选择日期"
        value-format="yyyy-MM-dd"
        :format="dateType === 1 ? 'yyyy 第 WW 周': 'yyyy-MM-dd' "
        append-to-body
        @change="dateChange"
      />
      <template v-if="page === 'first'">
        <div
          style="margin-left:20px;font-weight:bold;"
        >数据刷新间隔时间:</div>
        <el-select
          v-model="timeInterval"
          placeholder="请选择"
          filterable
          allow-create
          style="width:100px;margin:0 10px"
          @change="timeIntervalChange"
        >
          <el-option
            v-for="item in timeListCharts"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
        <div
          style="font-weight:bold;"
        >秒</div>
        <!-- <div
          v-if="dataType === 1"
          class="range-select"
        >
          <div style="font-weight:bold;">电缆范围:</div>
          <el-input-number
            v-model="min"
            :min="0"
            :controls="false"
            @change="minChange"
          />
          <span>~</span>
          <el-input-number
            v-model="max"
            :min="min"
            :controls="false"
            @change="maxChange"
          />
          <span>米</span>
        </div> -->
      </template>
      <div
        v-if="false"
        class="range-select"
      >
        <div style="font-weight:bold;">光缆位置:</div>
        <el-input-number
          v-model="position"
          :min="0"
          :controls="false"
          @change="positionChange"
        />
        <span>米</span>
      </div>
    </div>
    <div class="line_chart">
      <div style="height:35px;display:flex;align-items:flex-end;margin-bottom:15px;">
        <div class="title">{{ title }}</div>
        <div
          v-if="page === 'first'"
          class="time"
        >
          <img src="@/assets/page/<EMAIL>">
          <div>{{ `当前数据采集时间：${getDataTime || '---'}` }}</div>
        </div>
        <div
          v-if="page === 'second'"
          class="time"
        >
          <img
            src="@/assets/page/weizhi.png"
            style="width:18px;height:18px;"
          >
          <div>{{ `当前位置：${positionName}` }}</div>
        </div>
      </div>
      <div v-if="page === 'first'">
        <line-charts
          v-if="chartsData.xAxisData && chartsData.xAxisData.length>0 && dataType === 1"
          ref="chart"
          :page="page"
          :zoom-start="zoomStart"
          :zoom-end="zoomEnd"
          :charts-selected="chartsSelected"
          @zoomChange="zoomChange"
          @legendselectchanged="legendselectchanged"
        />
        <SensorChart
          v-if="dataType === 2"
          ref="sensorChartRef"
          :sensor="sensorData"
        />
        <div
          v-if="!chartsData.xAxisData.length"
          class="no_data"
        >
          <img
            src="@/assets/page/yypz_icon_zanwu.png"
          >
          <div>暂无数据</div>
        </div>

      </div>

      <div v-if="page === 'second'">
        <line-charts-point
          v-if="singleChartsData.xAxisData.length && singleChartsData.xAxisData.length > 0 && dataType === 1"
          ref="chartPoint"
          :prop-data="singleChartsData"
          :page="page"
        />
        <SensorPointChart
          v-if="singleChartsData.xAxisData.length && dataType === 2"
          ref="sensorPointRef"
          :alarm-level="alarmLevelList[2].levels"
        />
        <div
          v-if="!singleChartsData.xAxisData.length"
          class="no_data"
        >
          <img
            src="@/assets/page/yypz_icon_zanwu.png"
          >
          <div>暂无数据</div>
        </div>

      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import dayjs from 'dayjs'
import {
  deviceDropdown, dtsAlarmThreshold, liveTrend, singleLine, spaceCascadeDropdown
} from '@/api/lineCharts'
import * as echarts from 'echarts'
import { getDateRange, useDebounceFn } from '@/utils'
import { pointCurve, realTimeTrend, sensorTree } from '@/api/displacementSensor'
import SensorChart from '@/views/aobo/curveView/modules/SensorChart'
import SensorPointChart from '@/views/aobo/curveView/modules/SensorPointChart'
import { getAlarmLevelList } from '@/api/alarmTactics'
import { cableOptions, monitoringPoint } from '@/api/deviceManage'
import VirtualSelect from '@/components/VirtualSelect/Select'
import lineCharts from './modules/lineCharts'
import lineChartsPoint from './modules/lineChartsPoint'

const alarmColorMap = {
  0: '#0000FF',
  1: '#FFA500',
  2: '#e4e46f',
  3: '#FF0000',
}

/**
 * 递归遍历数据，根据id获取数据，并记录找到数据的父级链
 * */
function findDataById(data, id, parentChain = []) {
  for (let i = 0; i < data.length; i++) {
    if (data[i].id === Number(id)) {
      // 找到匹配的数据，返回数据和父级链
      return { data: data[i], parentChain: [...parentChain, data[i]] }
    } if (data[i].childList && data[i].childList.length > 0) {
      // 如果当前数据有子数据，递归遍历子数据
      const result = findDataById(data[i].childList, id, [...parentChain, data[i]])
      if (result) {
        // 如果在子数据中找到匹配的数据，返回结果
        return result
      }
    }
  }
  // 如果没有找到匹配的数据，返回null
  return null
}

export default {
  name: 'CurveView',
  components: {
    lineCharts, lineChartsPoint, SensorChart, SensorPointChart, VirtualSelect
  },
  data() {
    return {
      page: 'first',
      title: '实时温度趋势',
      getDataTime: null, // 当前数据采集时间
      opticalFiberList: null, // 选中光纤数组
      spaceId: null, // 选中空间
      idArray: [],
      deviceList: [], // 设备下拉框
      spaceList: [], // 空间下拉框
      nameObj: {
        1: 'areaStr',
        2: 'cabinetStr',
        3: 'tierStr',
        4: 'cellStr'
      },
      date: dayjs().format('YYYY-MM-DD'),
      colorList: [
        { color1: '#5470c6', color2: 'rgba(84, 112, 198, 0)' },
        { color1: '#91cc75', color2: 'rgba(145, 204, 117, 0)' },
        { color1: '#fac858', color2: 'rgba(250, 200, 88, 0)' },
        { color1: '#ee6666', color2: 'rgba(238, 102, 102, 0)' },
        { color1: '#73c0de', color2: 'rgba(115, 192, 222, 0)' },
        { color1: '#3ba272', color2: 'rgba(59, 162, 114, 0)' },
        { color1: '#fc8452', color2: 'rgba(252, 132, 82, 0)' },
        { color1: '#9a60b4', color2: 'rgba(154, 96, 180, 0)' },
        { color1: '#ea7ccc', color2: 'rgba(234, 124, 204, 0)' }
      ],
      chartsData: {
        xAxisData: [],
        seriesData: []
      },
      singleChartsData: {
        xAxisData: [],
        seriesData: []
      },
      lineChartData: null,
      loading: false,
      threshold: 0, // 阈值
      positionName: '', // 单点曲线当前电池位置
      PositionNameNum: 0,
      zoomStart: 0,
      zoomEnd: 100,
      interval: null,
      timeListCharts: [10, 30, 60], // 间隔时间下拉框
      timeInterval: Number(localStorage.getItem('timeInterval')) || 60,
      chartsSelected: {},
      firstLoad: true, // 是否是初次加载
      min: localStorage.getItem('min-range') || undefined,
      max: localStorage.getItem('max-range') || undefined,
      position: 0,
      dateType: 0,
      dateTypeList: [
        { label: '日', value: 0 },
        { label: '周', value: 1 },
        { label: '月', value: 2 },
        { label: '年', value: 3 }
      ],
      dateTypeMap: {
        0: 'date',
        1: 'week',
        2: 'month',
        3: 'year'
      },
      showPicker: true,
      flag: false, // 是否是从实时趋势跳转到单点曲线
      debouncedGetChartsData: useDebounceFn(this.getChartsData, 500),
      dataType: 1,
      dataTypeMap: {
        1: '温度',
        2: '位移传感器',
      },

      sensorList: [],
      sensor: null,
      pointId: null,
      sensorData: null,
      tempLevelList: [],
      alarmLevelList: [],
      monitoringPointList: [],
      monitoringPointId: null,
    }
  },
  computed: {
    ...mapGetters([
      'tableHeaderStyle', 'btnAuthority'
    ])
  },
  created() {
    // 获取路由传参
    const { id } = this.$route.query
    if (id) {
      this.page = 'second'
      this.spaceId = Number(id)
      this.tabChange()
    }
    // this.sensorDropdown()
    // this.cableDropdown()
    this.getAlarmLevelList()
  },

  beforeDestroy() {
    // console.log('beforeDestroy', this.interval)
    if (this.interval) {
      clearInterval(this.interval)
    }
  },
  async mounted() {
    console.log(this.sensorData)
    this.cableDropdownTime()
  },
  methods: {
    // 选择数据刷新间隔时间
    timeIntervalChange() {
      localStorage.setItem('timeInterval', this.timeInterval)
      if (this.interval) {
        clearInterval(this.interval)
        this.interval = setInterval(() => {
          this.getChartsData()
        }, Number(this.timeInterval) * 1000)
      }
    },
    /**
     * 获取等级列表
     * */
    getAlarmLevelList() {
      getAlarmLevelList()
        .then((res) => {
          this.alarmLevelList = res.data
          // 根据value排序，大的在前
          this.tempLevelList = res.data[0].levels.sort((a, b) => b.value - a.value)
          console.log('报警等级列表', this.tempLevelList)
        })
    },

    /**
     * 根据温度判断报警等级
     * */
    getAlarmLevel(temp) {
      if (!this.tempLevelList.length) return
      for (const item of this.tempLevelList) {
        if (temp >= item.value) {
          return item.level
        }
      }
    },

    tabChange() {
      this.title = this.page === 'first' ? '实时温度趋势' : '温度趋势'
      this.dataType = 1
      this.firstLoad = true
      this.dateType = 0
      this.loading = true
      this.date = dayjs().format('YYYY-MM-DD')
      if (this.interval) {
        clearInterval(this.interval)
        if (this.page === 'first') {
          this.interval = setInterval(() => {
            this.getChartsData()
          }, Number(this.timeInterval) * 1000)
        }
      }
      this.chartsData.xAxisData = []
      this.singleChartsData.xAxisData = []
      this.lineChartData = []
      // 提取查找第一个包含子项的元素逻辑，确保级联选择框有默认值
      const firstItemWithChild = this.sensorList.find((item) => item.childList && item.childList.length > 0)
      let firstItemWithArray = []
      if (firstItemWithChild) {
        firstItemWithArray = [firstItemWithChild.id, firstItemWithChild.childList[0].id]
        console.log('默认选中项:', firstItemWithArray)
      }

      if (this.page === 'first') {
        this.idArray = []
        this.spaceId = null
        this.cableDropdownTime()
      } else {
        // 单点曲线页面
        // this.idArray = firstItemWithArray
        this.opticalFiberList = null
        // 如果设置了默认值，触发级联选择变化事件
        this.$nextTick(() => {
          if (firstItemWithArray.length > 0) {
            this.idArray = firstItemWithArray || []
            this.cascaderChange(1)
          }
        })
      }
    },
    // // 获取报警阈值
    // dtsAlarmThreshold() {
    //   dtsAlarmThreshold().then((res) => {
    //     this.threshold = res.data.threshold
    //   })
    // },
    // // 设备下拉框
    // deviceDropdown() {
    //   clearInterval(this.interval)
    //   deviceDropdown().then((res) => {
    //     this.deviceList = res.data
    //     if (res.data && res.data.length && res.data[0].cableList && res.data[0].cableList.length) {
    //       this.opticalFiberList = [[res.data[0].id, res.data[0].cableList[0].id]]
    //     }
    //     this.getChartsData()
    //     this.interval = setInterval(() => {
    //       this.getChartsData()
    //     }, Number(this.timeInterval) * 1000)
    //   })
    // },
    /**
     * 位移传感器设备下拉
     * */
    sensorDropdown() {
      sensorTree().then((res) => {
        this.sensorList = res.data
        if (res.data?.length) {
          this.sensorData = res.data[0]
          this.sensor = res.data[0].id
          for (const item of res.data) {
            if (item.points?.length) {
              this.pointId = item.points[0].id
              return
            }
          }
        }
      })
    },
    // 电缆下拉（实时专用）
    cableDropdownTime() {
      clearInterval(this.interval)
      cableOptions({ withChild: true }).then((res) => {
        if (res.data && res.data.length > 0) {
          res.data.forEach((item) => {
            if (!Array.isArray(item.childList)) {
              this.$set(item, 'childList', [])
            }
          })
        }
        this.sensorList = res.data
        const firstItemWithChild = this.sensorList.find((item) => item.childList && item.childList.length > 0)
        if (firstItemWithChild) {
          this.opticalFiberList = [[firstItemWithChild.id, firstItemWithChild.childList[0].id]]
        }
        this.getChartsData()
        this.interval = setInterval(() => {
          this.getChartsData()
        }, Number(this.timeInterval) * 1000)
      })
    },
    // 电缆下拉(单点专用)
    cableDropdown() {
      cableOptions({ withChild: true }).then((res) => {
        // this.cableList = res.data
        // 递归处理数据，确保 childList 存在且为数组
        if (res.data && res.data.length > 0) {
          res.data.forEach((item) => {
            if (!Array.isArray(item.childList)) {
              this.$set(item, 'childList', [])
            }
          })
        }
        this.sensorList = res.data
      })
    },
    // 根据电缆获取监控段的所有点位
    getMonitoringPoint(type) {
      if (!this.idArray || !this.idArray.length) {
        this.loading = false
        return
      }
      const id = this.idArray[this.idArray.length - 1]
      monitoringPoint(id).then((res) => {
        this.monitoringPointList = res.data
        if (type === 1 && res.data.length) {
          this.monitoringPointId = this.monitoringPointList[0].id
          this.getTempPointData()
        } else {
          this.loading = false
          this.singleChartsData = {
            xAxisData: [],
            seriesData: []
          }
        }
      }).catch(() => {
        this.loading = false
      })
    },
    // 设备级联下拉框
    deviceCascaderChange(val) {
      console.log(val, 'val', this.opticalFiberList, 'this.opticalFiberList')
      this.firstLoad = true
      this.getChartsData()
    },
    // 设置name字段
    setName(arr) {
      arr.forEach((item) => {
        if (!item.name && item.spaceName) {
          this.$set(item, 'name', item.spaceName)
        }
        if (item.childList && item.childList.length) this.setName(item.childList)
      })
    },
    // 空间级联下拉框
    // spaceParagraphDropdown() {
    //   spaceCascadeDropdown().then((res) => {
    //     this.spaceList = res.data
    //     if (this.flag) return

    //     if (!this.spaceId) {
    //       // 默认选中第一条数据
    //       this.idArray.push(this.spaceList[0].id)
    //       if ((this.spaceList[0].childList || []).length) {
    //         this.idArray.push(this.spaceList[0].childList[0].id)
    //       }
    //       if ((this.spaceList[0].childList[0].childList || []).length) {
    //         this.idArray.push(this.spaceList[0].childList[0].childList[0].id)
    //       }
    //       this.spaceId = this.idArray[this.idArray.length - 1]
    //     } else {
    //       const result = findDataById(res.data, this.spaceId)
    //       this.idArray = result.parentChain.map((item) => item.id)
    //       if (result.data.childList?.length) {
    //         this.spaceId = result.data.childList[0].id
    //         this.idArray.push(this.spaceId)
    //       }
    //     }
    //     this.setPositionName(this.spaceList)
    //     this.getChartsDataPoint()
    //   })
    // },
    setPositionName(arr) {
      arr.forEach((item) => {
        if (item.id === this.idArray[this.PositionNameNum]) {
          this.positionName = this.PositionNameNum === 0
            ? `${item.name}`
            : `${this.positionName}/${item.name}`
          if (item.childList) {
            this.PositionNameNum++
            this.setPositionName(item.childList)
          } else {
            this.PositionNameNum = 0
          }
        }
      })
    },
    // 单点电缆下拉改变
    cascaderChange(type) {
      this.firstLoad = true
      // 设置当前位置
      this.positionName = ''
      // this.setPositionName(this.spaceList)

      // this.spaceId = this.idArray[this.idArray.length - 1]
      this.getMonitoringPoint(1)
      // this.getChartsDataPoint()
    },
    monitoringPointChange() {
      if (this.monitoringPointId) {
        this.getChartsDataPoint()
      }
    },
    // 图例选中改变
    legendselectchanged(e) {
      this.chartsSelected = e.chartsSelected
    },
    // 缩放比列改变
    zoomChange(e) {
      this.zoomStart = e.zoomStart
      this.zoomEnd = e.zoomEnd
    },
    dateChange() {
      this.firstLoad = true
      this.getChartsData()
    },
    // 获取实时趋势数据
    getChartsData() {
      if (this.page === 'second') {
        this.getChartsDataPoint()
        return
      }

      if (this.firstLoad) {
        this.firstLoad = false
        // this.loading = true
      }
      if (this.dataType === 1) {
        this.getTempRealData()
      } else {
        this.getSensorRealData()
      }
    },
    /**
     * 获取温度实时趋势数据
     * */
    getTempRealData() {
      if (!this.opticalFiberList) return
      const data = {
        // deviceIds: Array.from(new Set(this.opticalFiberList.map((item) => item[0]))),
        // cableIds: Array.from(new Set(this.opticalFiberList.map((item) => item[1]))),
        // date: this.date,
        segmentIds: Array.from(new Set(this.opticalFiberList.map((item) => item[1]))),
        // min: this.min * 100,
        // max: this.max * 100
      }

      this.loading = true
      console.log(data, 'data')
      liveTrend(data).then((res) => {
        this.chartsData = {
          xAxisData: [],
          seriesData: []
        }
        this.lineChartData = []
        console.log(res.data, res.data.length, 'res.data')
        if (!res.data || !res.data.segments.length) {
          return
        }
        console.log(res.data, 'res.data')
        // const segments = res.data.map((item) => {
        //   item.segments.forEach((item1) => {
        //     item1.deviceName = item.name
        //   })
        //   return item.segments
        // })
        const { segments } = res.data
        console.log(segments, 'segments')
        // 使用数组展开运算符和 concat 方法一次性合并数据，提升性能
        this.lineChartData = this.lineChartData.concat(...segments)
        console.log(this.lineChartData, 'this.lineChartData')
        // 判断哪一项有数据,如果都没有temperatures数据，则为暂无数据
        const haveDataArray = this.lineChartData.find((item) => (item.temperatures || []).length)
        console.log(haveDataArray, 'haveDataArrayhaveDataArray')
        this.getDataTime = haveDataArray.temperatures[0].time
        if (haveDataArray) {
          this.chartsData.xAxisData = haveDataArray.temperatures.map((item) => item.position)
        } else {
          this.lineChartData = []

          return
        }
        this.lineChartData.forEach((item) => {
          item.temperatures.forEach((item1) => {
            item1.fiberId = item.fiberId
            item1.startPosition = item.startPosition
          })
        })
        this.chartsData.seriesData = this.lineChartData.map((item, index) => {
          const datas = (item.temperatures || []).map((item1) => {
            // console.log(item1, 'item1')
            this.$set(item1, 'itemStyle', {
              color: alarmColorMap[this.getAlarmLevel(item1.value)] || '#00E667'
            })

            this.$set(item1, 'itemStyle', {
              color: alarmColorMap[this.getAlarmLevel(item1.value)] || '#00E667',
              opacity: this.getAlarmLevel(item1.value) !== undefined ? 1 : 0
            })
            item1.value = item1.temperature
            return [item1.position, (item1.value / 100).toFixed(2), item1]
          })
          const that = this
          return {
            name: `${item.name}`,
            type: 'line',
            data: datas,
            smooth: true, // 平滑曲线
            sampling: 'lttb',
            large: true,
            symbolSize(params) {
              return that.getAlarmLevel(params[1]) !== undefined ? 8 : 0
            },
            itemStyle: {
              normal: {
                color(params) {
                  // params 是当前数据点的信息
                  return alarmColorMap[that.getAlarmLevel(params.value[1])] || that.colorList[index].color1
                }
              }
            },
            lineStyle: { width: 2.5, color: this.colorList[index].color1 },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: this.colorList[index].color2 },
                { offset: 0.9, color: '#fff' }
              ])
            },
            label: {
              show: false,
              position: 'top',
              formatter: (val) => `${(val.value / 100).toFixed(2)}℃`
            }
          }
        })
        setTimeout(() => {
          this.$refs.chart?.updateOption(this.chartsData.seriesData)
        })
      }).finally(() => {
        this.loading = false
      })
    },
    /**
     * 获取位移传感器实时趋势数据
     * */
    getSensorRealData() {
      if (!this.sensor) return
      this.loading = true
      realTimeTrend(this.sensor).then((res) => {
        this.chartsData = {
          xAxisData: [],
          seriesData: []
        }

        if (!res.data.length) return
        const yData = []
        const xData = []
        for (const item of res.data) {
          this.chartsData.xAxisData.push(item.name)
          xData.push(item.name)
          yData.push(item.value)
        }
        setTimeout(() => {
          this.$refs.sensorChartRef?.updateOption(res.data)
        })
      }).finally(() => {
        this.loading = false
      })
    },
    // 获取单点曲线数据
    getChartsDataPoint() {
      if (this.dataType === 1) {
        this.getTempPointData()
      } else {
        this.getSensorTrend()
      }
    },
    /**
     * 获取温度单点曲线数据
     * */
    getTempPointData() {
      if (!this.idArray) return
      this.lineChartData = []

      const time = getDateRange(this.date, this.dateType, 'YYYY-MM-DD')
      this.page = 'second'
      const datas = {
        segmentId: this.idArray[this.idArray.length - 1],
        date: this.date,
        // timeUnit: this.dateType,
        // startDate: time.start,
        // endDate: time.end,
        // position: this.position * 100
        position: this.monitoringPointId,
      }
      if (this.firstLoad) {
        this.firstLoad = false
        this.loading = true
      }
      singleLine(datas).then((res) => {
        // if (this.flag) {
        //   this.flag = false
        //   this.spaceId = res.data.id
        //   const result = findDataById(this.spaceList, this.spaceId)
        //   this.idArray = result.parentChain.map((item) => item.id)
        // }

        this.positionName = res.data.cableStr + res.data.segmentStr

        this.singleChartsData = {
          xAxisData: [],
          seriesData: []
        }
        this.$nextTick(() => {
          this.lineChartData = res.data.temperatures || []
          const tempSet = new Set()
          for (const item of this.lineChartData) {
            tempSet.add(item.time)
          }
          this.singleChartsData.xAxisData = [...tempSet]
          if (!this.singleChartsData.xAxisData.length) return
          this.singleChartsData.seriesData = [{
            // name: el.name,
            type: 'line',
            data: res.data.temperatures.map((item) => {
              item.value = (item.temperature / 100).toFixed(2)
              return item
            }),
            smooth: true, // 平滑曲线
            symbolSize: 8,
            showAllSymbol: false,
            sampling: 'average',
            large: true,
            // showSymbol: true,
            symbol: 'none',
            // itemStyle: { color: '#1768EB' },
            // lineStyle: { width: 2.5, color: '#1768EB' },
            lineStyle: { width: 2.5 },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(48, 149, 251, 0.4)' },
                { offset: 0.9, color: '#fff' }
              ])
            },
            label: {
              show: false,
              position: 'top',
              formatter: (val) => `${(val.value / 100).toFixed(2)}℃`
            }
          }]
          for (const item of this.tempLevelList) {
            /** 阈值线*/
            this.singleChartsData.seriesData.push({
              name: '',
              type: 'line',
              data: [item.value],
              symbol: 'none',
              label: {
                show: false
              },
              markLine: {
                silent: true,
                data: [{
                  name: `阈值线：${item.value}℃`,
                  yAxis: item.value,
                  label: {
                    formatter: '{b}',
                    position: 'end',
                    color: alarmColorMap[item.level],
                  }
                }],
                lineStyle: {
                  color: alarmColorMap[item.level],
                  width: 2
                },
                symbol: 'none',
                label: {
                  distance: [20, 8]
                }
              }
            })
          }
        })
      }).finally(() => {
        this.loading = false
      })
    },
    /**
     * 获取位移传感器单点曲线数据
     * */
    getSensorTrend() {
      if (!this.pointId) return

      this.page = 'second'
      if (this.firstLoad) {
        this.firstLoad = false
        this.loading = true
      }
      const data = {
        segmentId: this.pointId,
        position: this.position, // 监控段位置
        date: this.date, // 日期
      }
      pointCurve(this.pointId).then((res) => {
        this.singleChartsData = {
          xAxisData: [],
          seriesData: []
        }
        console.log(res.data)
        const xData = []
        const yData = []
        for (const item of res.data) {
          this.singleChartsData.xAxisData.push(item.time)
          xData.push(item.time)
          yData.push(item.value)
        }

        setTimeout(() => {
          this.$refs.sensorPointRef?.updateOption(xData, yData)
        })
      }).finally(() => {
        this.loading = false
      })
    },
    rangeChange() {
      if (this.max <= this.min) {
        this.$message.error('请输入正确的范围')
        return
      }
      this.firstLoad = true
      this.debouncedGetChartsData()
    },
    minChange() {
      localStorage.setItem('min-range', this.min)
      this.rangeChange()
    },
    maxChange() {
      if (this.max) {
        localStorage.setItem('max-range', this.max)
      } else {
        localStorage.removeItem('max-range')
      }
      this.rangeChange()
    },
    positionChange() {
      this.getChartsDataPoint()
    },
    /**
     * 改变日期类型 日、周、月、年
     * */
    dateTypeChange(e) {
      this.dateChange()
      this.showPicker = false
      this.$nextTick(() => {
        this.showPicker = true
      })
    },
    /**
     * 切换数据类型
     * */
    dataTypeChange() {
      this.deviceCascaderChange()
      if (this.page === 'first') {
        this.title = this.dataType === 1 ? '实时温度趋势' : '位移传感器实时趋势'
      } else {
        this.title = this.dataType === 1 ? '温度趋势' : '位移传感器趋势'
      }
      this.$forceUpdate()
    },
    /**
     * 切换位移传感器设备
     * */
    sensorChange() {
      this.deviceCascaderChange()
      this.sensorData = this.sensorList.find((item) => item.id === this.sensor)
    }

  }

}
</script>

<style lang="scss" scoped>
.main-content{
  height: calc(100%);
  box-sizing: border-box;
  font-family: PingFang SC RE;
  will-change: transform;
  .top {
    display: flex;
    align-items: center;
    margin: 10px 0;
    .range-select {
      display: flex;
      align-items: center;
      gap: 0 10px;
      margin-left: 30px;
      .el-input-number {
        width: 70px;
      }
    }
  }
  .line_chart {
    width: 100%;
    // height: 100vh;
    position: relative;
    .time {
      display: flex;
      align-items: center;
      img {
        width: 15px;
        height: 15px;
        margin-right: 5px;
      }
      div {
        font-family: PingFang SC RE;
        color: #737377;
        font-size: 14px;
        font-weight: bold;
        display: flex;
      }
    }
    .title {
      font-size: 18px;
      font-family: PingFang SC RE;
      font-weight: bold;
      color: #202225;
      // margin: 25px 0 15px;
      margin-right: 50px;
      padding-left: 10px;
      border-left: 4px solid #1768EB;
    }
    .no_data {
      position: fixed;
      top: 90px;
      right: 50px;
      left: 100px;
      bottom: 20px;
      display: grid;
      justify-content: center;
      place-content: center;
      place-items: center;
      z-index: -1;
      img {
        width: 250px;
        height: 200px;
      }
      div {
        font-size: 20px;
        font-weight: bold;
        color: #7d7d7d;
        //margin-left: 50px;
      }
    }
  }
}
</style>
<style lang="scss">
.el-cascader-panel {
  .el-cascader-menu:nth-child(1) {
    li {
      .el-checkbox {
        display: none;
      }
    }
  }
}
</style>
