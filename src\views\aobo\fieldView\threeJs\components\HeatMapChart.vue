<script>
import Vue from 'vue'
import * as echarts from 'echarts'
import { getCoreTemp } from '@/api/onSiteView'
import { alarmColorMap } from '@/constants'

function hexToRgb(hex) {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
      }
    : null
}

// 颜色插值函数
function interpolateColor(color1, color2, ratio) {
  const c1 = hexToRgb(color1)
  const c2 = hexToRgb(color2)

  const r = Math.round(c1.r + (c2.r - c1.r) * ratio)
  const g = Math.round(c1.g + (c2.g - c1.g) * ratio)
  const b = Math.round(c1.b + (c2.b - c1.b) * ratio)

  return `rgb(${r}, ${g}, ${b})`
}

// 温度到颜色的映射函数
function getColorByTemperature(temp, _) {
  const colors = [
    { temp: 90, color: '#8B0000' }, // 深红色 - 最热
    { temp: 80, color: '#FF0000' }, // 红色
    { temp: 70, color: '#FF7F00' }, // 橙色
    { temp: 60, color: '#FFFF00' }, // 黄色
    { temp: 50, color: '#7FFF00' }, // 黄绿色
    { temp: 40, color: '#00FFFF' }, // 青色
    { temp: 30, color: '#00BFFF' }, // 深天蓝色
    { temp: 20, color: '#0000FF' }, // 蓝色
    { temp: 10, color: '#000080' }, // 深蓝色 - 最冷
  ]

  // 边界情况处理
  if (temp >= colors[0].temp) return alarmColorMap[3]
  if (temp <= colors[colors.length - 1].temp) return alarmColorMap[0]

  // 查找温度区间并进行线性插值
  for (let i = 0; i < colors.length - 1; i++) {
    if (temp <= colors[i].temp && temp >= colors[i + 1].temp) {
      const ratio = (colors[i].temp - temp) / (colors[i].temp - colors[i + 1].temp)
      return interpolateColor(colors[i].color, colors[i + 1].color, ratio)
    }
  }
}

export default Vue.extend({
  name: 'HeatMapChart',
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    tempLevelList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loading: true,
      tempList: [],
      colorList: [],
    }
  },
  created() {
    this.colorList = []
    for (const item of this.tempLevelList) {
      this.colorList.push({ color: alarmColorMap[item.level], temp: item.value })
    }
    this.getCoreTemp()
  },
  mounted() {},
  beforeDestroy() {},
  methods: {
    /**
     * 获取缆芯温度
     * */
    getCoreTemp() {
      this.loading = true
      getCoreTemp({ current: this.data.parentData.current || 0, surfaceTemp: Math.floor(this.data.temperature / 100) })
        .then((res) => {
          this.tempList = Object.values(res.data)
          this.initChart()
        })
        .finally(() => {
          this.loading = false
        })
    },

    initChart() {
      const chart = echarts.init(this.$refs.chartRef)

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            const temperature = params.data
            return `温度: ${Math.round(temperature * 100) / 100}°C<br/>电流: ${this.data.parentData.current}A`
          },
        },
        xAxis: {
          type: 'value',
          show: false,
          min: 0,
          max: 300,
        },
        yAxis: {
          type: 'value',
          show: false,
          min: 0,
          max: 300,
        },
        series: [
          {
            type: 'custom',
            coordinateSystem: 'cartesian2d',
            data: this.tempList,
            renderItem: (params, api) => {
              const w = api.getWidth()
              const h = api.getHeight()
              const temp = api.value(0)
              // 分成5组，每一组的数量
              const groupSize = Math.ceil(this.tempList.length / 5)
              const groupIndex = Math.floor(params.dataIndex / groupSize)
              // 每一份圆环长度
              const unitLength = Math.min(w, h) / 2 / (this.tempList.length + 1)

              // const colors = alarmColorMap

              const fillColor = getColorByTemperature(temp, this.colorList)
              console.log((params.dataIndex + 1) % groupSize)

              return {
                type: 'sector',
                shape: {
                  cx: w / 2,
                  cy: h / 2,
                  r0: unitLength * params.dataIndex,
                  r: unitLength * (params.dataIndex + 1),
                  startAngle: 0,
                  endAngle: Math.PI * 2,
                },
                style: {
                  fill: fillColor,
                  stroke: '#FEF1D84D',
                  lineWidth: (params.dataIndex + 1) % groupSize === 0 ? unitLength : 0,
                },
              }
            },
          },
        ],
      }

      chart.setOption(option)
    },
  },
})
</script>

<template>
  <div v-loading="loading" style="display: flex; justify-content: space-between; padding: 20px">
    <div ref="chartRef" style="width: 400px; height: 400px"></div>
    <aside>
      <header style="font-size: 20px">{{ data.parentData.parentCube }} - {{ data.parentData.name }} - {{ data.cablePosition / 100 }}m处</header>
      <div style="font-size: 18px; margin-top: 12px">
        <span>长度：</span>
        <span>{{ data.parentData.parentData.length / 100 }}m</span>
      </div>

      <div style="font-size: 18px; margin-top: 12px">
        <span>截面积：</span>
        <span>{{ data.parentData.parentData.crossSectionalArea }}mm²</span>
      </div>

      <div style="font-size: 18px; margin-top: 12px">
        <span>最大电流：</span>
        <span>{{ data.parentData.parentData.current }}A</span>
      </div>
    </aside>
  </div>
</template>

<style scoped lang="scss"></style>
