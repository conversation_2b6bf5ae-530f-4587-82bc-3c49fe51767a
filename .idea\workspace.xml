<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="031b2ea2-4304-4e1f-808f-ba85151d3cd4" name="更改" comment="fix: 现场视图">
      <change beforePath="$PROJECT_DIR$/src/views/aobo/fieldView/threeJs/components/HeatMapChart.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/aobo/fieldView/threeJs/components/HeatMapChart.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="JavaScript File" />
        <option value="Vue Options API Component" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="f79b8761fceb02c98b991d35412cf4db97caea7c" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://D:/Tencent/WeChat/xwechat_files/wxid_rd1gpmri91ee22_013e/msg/file/2025-08/管道.html" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="30zJLl0puAkEZFoR5CtEhU3Lr9K" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "JavaScript 调试.localhost:8999.executor": "Debug",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager.252": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "main",
    "junie.onboarding.icon.badge.shown": "true",
    "last_opened_file_path": "E:/Vankey/power-grid/csg-dts-web/src/views/aobo/fieldView/components",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "npm.build:prod.executor": "Run",
    "npm.dev.executor": "Run",
    "settings.editor.selected.configurable": "preferences.lookFeel",
    "to.speed.mode.migration.done": "true",
    "ts.external.directory.path": "D:\\JetBrains\\WebStorm 2024.1.3\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "vue.recent.templates": [
      "Vue Options API Component"
    ]
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\Vankey\power-grid\csg-dts-web\src\views\aobo\fieldView\components" />
      <recent name="E:\Vankey\power-grid\csg-dts-web\public\model" />
      <recent name="E:\Vankey\power-grid\csg-dts-web\public\texture" />
      <recent name="E:\Vankey\power-grid\csg-dts-web\src\assets\center-line" />
      <recent name="E:\Vankey\power-grid\csg-dts-web\public" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\Vankey\power-grid\csg-dts-web\src\views\aobo\fieldView\threeJs" />
      <recent name="E:\Vankey\power-grid\csg-dts-web\src\assets\onSiteView" />
      <recent name="E:\Vankey\power-grid\csg-dts-web\public\model" />
      <recent name="E:\Vankey\power-grid\csg-dts-web\public\texture" />
      <recent name="E:\Vankey\power-grid\csg-dts-web\src\assets\center-line" />
    </key>
  </component>
  <component name="RunManager" selected="npm.dev">
    <configuration name="localhost:8999" type="JavascriptDebugType" temporary="true" nameIsGenerated="true" uri="http://localhost:8999/">
      <method v="2" />
    </configuration>
    <configuration name="build:prod" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="build:prod" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="dev" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="npm.dev" />
        <item itemvalue="npm.build:prod" />
        <item itemvalue="JavaScript 调试.localhost:8999" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-e03c56caf84a-JavaScript-WS-252.23892.411" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="031b2ea2-4304-4e1f-808f-ba85151d3cd4" name="更改" comment="" />
      <created>1754624415863</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754624415863</updated>
      <workItem from="1754624418213" duration="5939000" />
      <workItem from="1754874806925" duration="1369000" />
      <workItem from="1754876368486" duration="2173000" />
      <workItem from="1754878594105" duration="21158000" />
      <workItem from="1754961115606" duration="16777000" />
      <workItem from="1755056249347" duration="10993000" />
      <workItem from="1755136584108" duration="8635000" />
      <workItem from="1755220160900" duration="27501000" />
      <workItem from="1755479458423" duration="19524000" />
      <workItem from="1755565248015" duration="26873000" />
      <workItem from="1755652156665" duration="27338000" />
      <workItem from="1755738352000" duration="28814000" />
      <workItem from="1755824725408" duration="28178000" />
      <workItem from="1756084192054" duration="26796000" />
      <workItem from="1756170294122" duration="27271000" />
      <workItem from="1756256553589" duration="6097000" />
      <workItem from="1756263161786" duration="12971000" />
      <workItem from="1756280045878" duration="7262000" />
      <workItem from="1756343529579" duration="37130000" />
    </task>
    <task id="LOCAL-00001" summary="feat: 项目初始化">
      <option name="closed" value="true" />
      <created>1754642645649</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1754642645649</updated>
    </task>
    <task id="LOCAL-00002" summary="feat: 引入模型并添加加载动画">
      <option name="closed" value="true" />
      <created>1754895710626</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1754895710626</updated>
    </task>
    <task id="LOCAL-00003" summary="feat: 全屏时触发画布尺寸更新">
      <option name="closed" value="true" />
      <created>1754896011074</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1754896011074</updated>
    </task>
    <task id="LOCAL-00004" summary="feat: 部分模型更换材质">
      <option name="closed" value="true" />
      <created>1754906158292</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1754906158292</updated>
    </task>
    <task id="LOCAL-00005" summary="feat: 性能优化">
      <option name="closed" value="true" />
      <created>1754967581343</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1754967581343</updated>
    </task>
    <task id="LOCAL-00006" summary="feat: 添加去首部、尾部快捷按钮">
      <option name="closed" value="true" />
      <created>1754969575996</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1754969575996</updated>
    </task>
    <task id="LOCAL-00007" summary="feat: 添加更多管道中轴线数据">
      <option name="closed" value="true" />
      <created>1755160226401</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1755160226401</updated>
    </task>
    <task id="LOCAL-00008" summary="feat: 自行绘制管道">
      <option name="closed" value="true" />
      <created>1755242465697</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1755242465697</updated>
    </task>
    <task id="LOCAL-00009" summary="feat: 管道分段绘制">
      <option name="closed" value="true" />
      <created>1755248818888</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1755248818888</updated>
    </task>
    <task id="LOCAL-00010" summary="feat: 补全管道数据">
      <option name="closed" value="true" />
      <created>1755486059538</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1755486059538</updated>
    </task>
    <task id="LOCAL-00011" summary="feat: 补全管道数据">
      <option name="closed" value="true" />
      <created>1755499674350</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1755499674350</updated>
    </task>
    <task id="LOCAL-00012" summary="feat(现场试图): 添加报警闪烁效果">
      <option name="closed" value="true" />
      <created>1755571579743</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1755571579743</updated>
    </task>
    <task id="LOCAL-00013" summary="feat(现场试图): 分段效果修改、添加选中效果">
      <option name="closed" value="true" />
      <created>1755596419470</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1755596419470</updated>
    </task>
    <task id="LOCAL-00014" summary="feat(现场试图): 绘制告警电网demo">
      <option name="closed" value="true" />
      <created>1755652766010</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1755652766010</updated>
    </task>
    <task id="LOCAL-00015" summary="feat(现场试图): 分段接入接口、数据间隔刷新">
      <option name="closed" value="true" />
      <created>1755741087462</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1755741087462</updated>
    </task>
    <task id="LOCAL-00016" summary="feat(现场试图): 分段点击事件相机视角优化">
      <option name="closed" value="true" />
      <created>1755744952511</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1755744952511</updated>
    </task>
    <task id="LOCAL-00017" summary="feat(现场试图): 分段弹窗">
      <option name="closed" value="true" />
      <created>1755762385778</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1755762385778</updated>
    </task>
    <task id="LOCAL-00018" summary="feat(现场试图): 管道添加详情弹窗">
      <option name="closed" value="true" />
      <created>1755767064408</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1755767064409</updated>
    </task>
    <task id="LOCAL-00019" summary="feat(现场试图): 接入dts主机信息">
      <option name="closed" value="true" />
      <created>1755768821839</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1755768821839</updated>
    </task>
    <task id="LOCAL-00020" summary="feat(现场试图): 光照优化;添加模型精度选择功能">
      <option name="closed" value="true" />
      <created>1755847102605</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1755847102605</updated>
    </task>
    <task id="LOCAL-00021" summary="feat(现场试图): 管道和分段详情展示点击的具体位置">
      <option name="closed" value="true" />
      <created>1755852416084</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1755852416084</updated>
    </task>
    <task id="LOCAL-00022" summary="feat(现场试图): 告警点位展示">
      <option name="closed" value="true" />
      <created>1756092784290</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1756092784290</updated>
    </task>
    <task id="LOCAL-00023" summary="feat(现场试图): 移除导出相关功能代码">
      <option name="closed" value="true" />
      <created>1756093158278</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1756093158278</updated>
    </task>
    <task id="LOCAL-00024" summary="feat(现场试图): 移除位移告警列表">
      <option name="closed" value="true" />
      <created>1756104029706</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1756104029706</updated>
    </task>
    <task id="LOCAL-00025" summary="feat(现场试图): 移除告警处置功能">
      <option name="closed" value="true" />
      <created>1756104277174</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1756104277174</updated>
    </task>
    <task id="LOCAL-00026" summary="feat(现场试图): 告警点位使用InstancedMesh绘制优化性能">
      <option name="closed" value="true" />
      <created>1756111918045</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1756111918045</updated>
    </task>
    <task id="LOCAL-00027" summary="feat(现场试图): 点击事件注册优化">
      <option name="closed" value="true" />
      <created>1756114224706</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1756114224707</updated>
    </task>
    <task id="LOCAL-00028" summary="feat(现场试图): 点击事件性能优化；点位根据告警等级区分不同颜色">
      <option name="closed" value="true" />
      <created>1756183832552</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1756183832553</updated>
    </task>
    <task id="LOCAL-00029" summary="feat(现场试图): 分段弹窗显示告警等级">
      <option name="closed" value="true" />
      <created>1756187071719</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1756187071719</updated>
    </task>
    <task id="LOCAL-00030" summary="feat(现场试图): 告警列表展示及点击详情跳转到对应点位">
      <option name="closed" value="true" />
      <created>1756189663095</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1756189663095</updated>
    </task>
    <task id="LOCAL-00031" summary="feat(现场试图): 告警点位显示优化">
      <option name="closed" value="true" />
      <created>1756257439021</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1756257439021</updated>
    </task>
    <task id="LOCAL-00032" summary="feat(现场试图): 详情弹窗移至告警点位">
      <option name="closed" value="true" />
      <created>1756273418905</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1756273418905</updated>
    </task>
    <task id="LOCAL-00033" summary="feat(现场试图): 移除冗余代码">
      <option name="closed" value="true" />
      <created>1756274403778</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1756274403778</updated>
    </task>
    <task id="LOCAL-00034" summary="refactor(现场试图): 详情弹窗重构">
      <option name="closed" value="true" />
      <created>1756276530232</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1756276530232</updated>
    </task>
    <task id="LOCAL-00035" summary="refactor(现场试图): socket使用代理配置">
      <option name="closed" value="true" />
      <created>1756278667897</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1756278667897</updated>
    </task>
    <task id="LOCAL-00036" summary="refactor(现场试图): 告警点位详情截面分析">
      <option name="closed" value="true" />
      <created>1756366201569</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1756366201570</updated>
    </task>
    <task id="LOCAL-00037" summary="refactor(现场试图): 定时器优化">
      <option name="closed" value="true" />
      <created>1756367696097</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1756367696097</updated>
    </task>
    <task id="LOCAL-00038" summary="refactor(现场试图): 数据变化后更新弹窗信息">
      <option name="closed" value="true" />
      <created>1756371311772</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1756371311772</updated>
    </task>
    <task id="LOCAL-00039" summary="refactor(现场试图): 更新popup信息优化">
      <option name="closed" value="true" />
      <created>1756372753755</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1756372753755</updated>
    </task>
    <task id="LOCAL-00040" summary="fix: 现场视图；单点曲线下拉框换成虚拟下拉框">
      <option name="closed" value="true" />
      <created>1756431750282</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1756431750282</updated>
    </task>
    <task id="LOCAL-00041" summary="fix: 现场视图">
      <option name="closed" value="true" />
      <created>1756435615690</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1756435615690</updated>
    </task>
    <option name="localTasksCounter" value="42" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="feat(现场试图): 分段弹窗" />
    <MESSAGE value="feat(现场试图): 管道添加详情弹窗" />
    <MESSAGE value="feat(现场试图): 接入dts主机信息" />
    <MESSAGE value="feat(现场试图): 光照优化;添加模型精度选择功能" />
    <MESSAGE value="feat(现场试图): 管道和分段详情展示点击的具体位置" />
    <MESSAGE value="feat(现场试图): 告警点位展示" />
    <MESSAGE value="feat(现场试图): 移除导出相关功能代码" />
    <MESSAGE value="feat(现场试图): 移除位移告警列表" />
    <MESSAGE value="feat(现场试图): 移除告警处置功能" />
    <MESSAGE value="feat(现场试图): 告警点位使用InstancedMesh绘制优化性能" />
    <MESSAGE value="feat(现场试图): 点击事件注册优化" />
    <MESSAGE value="feat(现场试图): 点击事件性能优化；点位根据告警等级区分不同颜色" />
    <MESSAGE value="feat(现场试图): 分段弹窗显示告警等级" />
    <MESSAGE value="feat(现场试图): 告警列表展示及点击详情跳转到对应点位" />
    <MESSAGE value="feat(现场试图): 告警点位显示优化" />
    <MESSAGE value="feat(现场试图): 详情弹窗移至告警点位" />
    <MESSAGE value="feat(现场试图): 移除冗余代码" />
    <MESSAGE value="refactor(现场试图): 详情弹窗重构" />
    <MESSAGE value="refactor(现场试图): socket使用代理配置" />
    <MESSAGE value="refactor(现场试图): 告警点位详情截面分析" />
    <MESSAGE value="refactor(现场试图): 定时器优化" />
    <MESSAGE value="refactor(现场试图): 数据变化后更新弹窗信息" />
    <MESSAGE value="refactor(现场试图): 更新popup信息优化" />
    <MESSAGE value="fix: 现场视图；单点曲线下拉框换成虚拟下拉框" />
    <MESSAGE value="fix: 现场视图" />
    <option name="LAST_COMMIT_MESSAGE" value="fix: 现场视图" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/views/aobo/curveView/index.vue</url>
          <line>607</line>
          <properties lambdaOrdinal="-1" />
          <option name="timeStamp" value="2" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>