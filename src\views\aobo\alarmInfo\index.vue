<script>
import Temperature from '@/views/aobo/alarmInfo/modules/Temperature'
import Sensor from '@/views/aobo/alarmInfo/modules/Sensor'

export default {
  name: 'AlarmInfo',
  components: { Temperature, Sensor },
  data() {
    return {
      activeTab: '1',
    }
  },
}
</script>

<template>
  <div style="padding-top:20px;box-sizing:border-box;">
    <!-- <el-tabs v-model="activeTab">
      <el-tab-pane label="温度" name="1" />
      <el-tab-pane label="位移传感器" name="2" />
    </el-tabs> -->
    <Temperature v-if="activeTab === '1'" />
    <Sensor v-if="activeTab === '2'" />
  </div>
</template>

<style scoped lang="scss"></style>
