<template>
  <div class="main-content">
    <div class="top">
      <div class="left">
        <div class="pa_name">系统管理</div>
        <div class="ch_name">/</div>
        <div class="ch_name">用户管理</div>
      </div>
    </div>
    <div class="line" />
    <div
      class="content-table"
    >
      <div class="title">
        <el-input
          v-model="keyword"
          suffix-icon="el-icon-search"
          class="input_style"
          placeholder="请输入搜索内容"
          @clear="getList(1)"
          @blur="getList(1)"
        />
        <div>
          <el-button
            type="primary"
            :disabled="!btnAuthorityList.includes('add')"
            @click="handleClick('new')"
          >新增
          </el-button>
          <el-button
            type="primary"
            :disabled="!btnAuthorityList.includes('import')"
            @click="handleClick('import')"
          >导入
          </el-button>
          <el-button
            type="success"
            :disabled="!btnAuthorityList.includes('export')"
            style="background-color:#02C69E;border-color: #02C69E"
            @click="bacthExport()"
          >导出
          </el-button>
        </div>
      </div>
      <el-table
        v-loading="loading"
        :header-cell-style="tableHeaderStyle"
        header-row-class-name="table-header"
        :data="tableData"
        stripe
        style="width: 100%"
        height="61vh"
        row-key="id"
      >
        <el-table-column
          label="序号"
          type="index"
          width="100"
          align="center"
        />
        <el-table-column
          prop="nickName"
          label="用户名"
          show-overflow-tooltip
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.nickName || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="username"
          label="账号"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.username || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="sex"
          label="性别"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.sex === 0 ? '女' : '男' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="phone"
          label="电话"
          show-overflow-tooltip
          align="center"
        >
          <template slot-scope="scope">
            <span v-if="!scope.row.showPhone">{{ scope.row.phone || '--' }}</span>
            <span v-if="scope.row.showPhone">{{ scope.row.phoneNew }}</span>
            <svg-icon style="margin-left:10px" :icon-class="scope.row.showPhone ? 'eye-open' : 'eye'" @click="getShowPhone(scope.row,scope.$index)" />
          </template>
        </el-table-column>
        <el-table-column
          prop="roleName"
          label="角色名称"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.roleName || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="isEnable"
          label="状态"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <el-switch
              :value="scope.row.isEnable"
              @change="stateChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column
          v-if="btnArrList.length"
          label="操作"
          fixed="right"
          align="center"
          :width="btnArrList.length * 65"
        >
          <template v-slot="scope">
            <el-button
              type="text"
              :disabled="scope.row.status === 1 || !btnAuthorityList.includes('update')"
              @click="handleClick('edit', scope.row)"
            >编辑
            </el-button>
            <el-button
              type="text"
              :disabled="scope.row.status === 1 || !btnAuthorityList.includes('reset-pwd')"
              :style="{color: scope.row.status === 1 || !btnAuthorityList.includes('reset-pwd') ? '' : '#02C69E'}"
              @click="handleClick('resetPassword', scope.row)"
            >重置密码
            </el-button>
            <el-button
              type="text"
              :disabled="scope.row.status === 1 || !btnAuthorityList.includes('del')"
              :style="{color: scope.row.status === 1 || !btnAuthorityList.includes('del') ? '' : '#FF4242'}"
              @click="handleClick('del', scope.row)"
            >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page.sync="pageNum"
        :page-size="pageSize"
        layout="total,prev, pager, next,sizes, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增 -->
    <el-dialog
      :title="type===0 ? '新增' : '编辑'"
      :visible.sync="dialogVisible"
      width="480px"
      :modal-append-to-body="false"
      top="30vh"
      @close="closeDialog()"
    >
      <div
        v-if="dialogVisible"
        style="display:flex;align-items:center;padding-left:32px"
      >
        <el-form
          ref="formNew"
          :model="form"
          label-width="100px"
          :rules="type===0? newRules : rules"
        >
          <el-form-item
            label="用户名"
            prop="nickName"
          >
            <el-input
              v-model="form.nickName"
              style="width:250px;"
            />
          </el-form-item>
          <el-form-item
            label="账号"
            prop="username"
          >
            <el-input
              v-model="form.username"
              style="width:250px;"
            />
          </el-form-item>
          <el-form-item
            label="密码"
            prop="password"
          >
            <el-input
              v-model="form.password"
              :disabled="type!==0"
              show-password
              style="width:250px;"
            />
          </el-form-item>
          <el-form-item label="性别">
            <el-select
              v-model="form.sex"
              placeholder="请选择"
              style="width:250px;"
            >
              <el-option
                v-for="item in genders"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="电话"
            prop="phone"
          >
            <el-input
              v-model="form.phone"
              style="width:250px;"
            />
          </el-form-item>
          <el-form-item
            label="角色名称"
            prop="roleIds"
          >
            <el-select
              v-model="form.roleIds"
              multiple
              collapse-tags
              placeholder="请选择"
              style="width:250px;"
            >
              <el-option
                v-for="item in roles"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-switch
              v-model="form.isEnable"
              @change="formStateChange()"
            />
          </el-form-item>
        </el-form>
      </div>
      <div
        slot="footer"
        class="dialog_footer"
      >
        <el-button
          type="primary"
          plain
          style="margin-right:10px"
          @click="closeDialog()"
        >取 消
        </el-button>
        <el-button
          type="primary"
          @click="submitNew()"
        >确 定
        </el-button>
      </div>
    </el-dialog>
    <!-- 删除 -->
    <el-dialog
      title="删除"
      :visible.sync="delDialogVisible"
      width="450px"
      :modal-append-to-body="false"
      top="30vh"
      @close="closeDelDialog()"
    >
      <span>确认删除用户？</span>
      <div
        slot="footer"
        class="dialog_footer"
      >
        <el-button
          type="primary"
          plain
          style="margin-right:10px"
          @click="closeDelDialog()"
        >取 消
        </el-button>
        <el-button
          type="primary"
          @click="doDel()"
        >确 定
        </el-button>
      </div>
    </el-dialog>
    <!-- 导入 -->
    <el-dialog
      title="导入"
      :visible.sync="importDialogVisible"
      width="fit-content"
      :modal-append-to-body="false"
      top="30vh"
      @close="closeImportDialog()"
    >
      <div style="padding:0 30px 0;">
        <div style="display:flex;justify-content: space-between;margin-bottom: 30px;align-items: center">
          <div
            slot="tip"
            class="uploadText1"
          >选择文件
            <div class="uploadText2">(支持扩展名:xls、xlsx)</div>
          </div>
          <el-link
            type="primary"
            @click="downloadTemplate"
          >点击此处下载模板</el-link>
        </div>
        <el-upload
          class="upload-demo"
          action="#"
          :auto-upload="false"
          :show-file-list="false"
          drag
          :on-change="handleImportSubmit"
        >
          <i
            v-show="false"
            class="el-icon-upload"
          />

          <img
            src="@/assets/userManage/<EMAIL>"
            style="width: 25.8px;height: 24.5px;margin: 17% 0 13px;"
          >
          <div class="el-upload__text"><em>选择上传</em></div>
        </el-upload>
      </div>
      <div
        slot="footer"
        class="dialog_footer"
      >
        <!-- <el-button
          type="primary"
          plain
          style="margin-right:10px"
          @click="closeImportDialog()"
        >取 消
        </el-button>
        <el-button
          type="primary"
          @click="handleAlarmSubmit()"
        >确 定
        </el-button> -->
      </div>
    </el-dialog>

    <el-dialog
      :title="resultData.successTotal ? '导入成功' : '导入失败'"
      :visible.sync="dialogVisibleImport"
      width="480px"
      :before-close="handleCloseImport"
      :modal-append-to-body="false"
    >
      <import-result
        :result-data="resultData"
        @handleClose="handleCloseImport"
        @submitForm="submitFormImport"
        @downFail="downFail"
      />
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { deepClone, utf8to16 } from '@/utils'
import {
  add,
  del,
  doPage,
  enableOrDisable,
  getRoleList,
  resetPassword,
  update,
  download,
  importExcel,
  bacthExport,
  getPhone
} from '@/api/aobo/userManagement'
// import commonBtn from '@/components/commonBtn/index'
import importResult from '@/components/importResult'

export default {
  name: 'AlarmInfo',
  components: {
    // commonBtn,
    importResult
  },
  data() {
    return {
      btnAuthorityList: [], // 按钮权限
      type: 0,
      delDialogVisible: false,
      delUserId: 0,
      rules: {
        nickName: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
          // { min: 3, max: 5, message: '长度在 3 到 5 个字符', trigger: 'blur' }
        ],
        username: [
          { required: true, message: '请输入账号', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入电话', trigger: 'blur' }
        ],
        roleIds: [
          { required: true, message: '请选择角色名称', trigger: 'change' }
        ]
      },
      newRules: {
        nickName: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
          // { min: 3, max: 5, message: '长度在 3 到 5 个字符', trigger: 'blur' }
        ],
        username: [
          { required: true, message: '请输入账号', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入电话', trigger: 'blur' }
        ],
        roleIds: [
          { required: true, message: '请选择角色名称', trigger: 'change' }
        ]
      },
      roles: [{
        id: '1',
        name: '普通员工'
      }],
      genders: [{
        value: 1,
        label: '男'
      }, {
        value: 0,
        label: '女'
      }],
      form: {
        id: '',
        nickName: '',
        username: '',
        password: '',
        sex: '',
        phone: '',
        roleIds: '',
        isEnable: false
      },
      test: false,
      date: [],
      status: null, // 状态
      labelPosition: 'right',
      position: null, // 煤矿位置
      keyword: '', // 煤矿位置
      btnArrList: [
        {
          type: 'list',
          name: '处置',
          tagName: 'handle',
          color: '#1071E2'
        },
        {
          type: 'list',
          name: '处置',
          tagName: 'handle',
          color: '#1071E2'
        },
        {
          type: 'list',
          name: '处置',
          tagName: 'handle',
          color: '#1071E2'
        }
      ],
      operationList: ['handle'],
      tableData: [], // 列表数据
      loading: false,
      pageNum: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      importDialogVisible: false,
      alarmNum: 1, // 间隔时间
      select: null, // 选中数据
      selectList: {}, // 选中数据
      resultData: {}, // 导入结果
      dialogVisibleImport: false
    }
  },
  computed: {
    ...mapGetters([
      'tableHeaderStyle', 'btnAuthority'
    ])
  },
  created() {
    const cur = this.btnAuthority.find((item) => item.code === this.$route.meta.code)
    if (cur) this.btnAuthorityList = deepClone(cur.functionPermissionCode)
    console.log(this.btnAuthorityList, 'btnAuthorityList')
  },
  mounted() {
    this.getRoleList()
    this.getList(1)
  },
  methods: {
    getRoleList() {
      getRoleList().then((res) => {
        this.roles = res.data
      })
    },
    formStateChange() {

    },
    stateChange(data) {
      enableOrDisable(data.id).then((res) => {
        this.$message.success('成功')
        this.getList()
      }).catch(() => {
        this.$message.warning('失败')
        this.getList()
      })
    },
    getList(isPage) {
      if (isPage) {
        this.pageNum = 1
        this.selectList = []
      }
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        query: {},
        keyword: this.keyword
      }
      this.loading = true
      doPage(params).then((res) => {
        this.tableData = res.data.records.map((item) => item)
        this.pageNum = res.data.current
        this.total = Number(res.data.total)
      }).finally(() => {
        this.loading = false
      })
    },
    // 改变每页条数
    handleSizeChange(val) {
      this.pageSize = val
      this.getList()
    },
    // 分页
    handleCurrentChange(val) {
      this.pageNum = val
      this.getList()
    },

    submitNew() {
      this.$refs.formNew.validate((valid) => {
        if (valid) {
          if (this.type === 0) {
            // 新增
            add({
              nickName: this.form.nickName,
              username: this.form.username,
              sex: this.form.sex,
              password: this.form.password,
              roleIds: this.form.roleIds,
              phone: this.form.phone,
              isEnable: this.form.isEnable ? 1 : 0
            }).then((res) => {
              this.$message.success('成功')
              this.getList(1)
              this.closeDialog()
            })
          } else {
            // 编辑
            update({
              id: this.form.id,
              nickName: this.form.nickName,
              username: this.form.username,
              sex: this.form.sex,
              password: '',
              roleIds: this.form.roleIds,
              phone: this.form.phone,
              isEnable: this.form.isEnable ? 1 : 0
            }).then((res) => {
              this.$message.success('成功')
              this.getList(1)
              this.closeDialog()
            })
          }
        }
      })
    },

    closeDelDialog() {
      this.delDialogVisible = false
    },
    closeDialog() {
      this.form = {
        id: '',
        nickName: '',
        username: '',
        password: '',
        sex: '',
        phone: '',
        roleIds: '',
        isEnable: false
      }
      this.dialogVisible = false
      this.$refs.formNew.resetFields()
      this.$refs.formNew.clearValidate()
    },
    closeImportDialog() {
      this.importDialogVisible = false
      this.select = null
    },
    handleNew() {
      this.type = 0
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.formNew.clearValidate()
      })
    },
    handleImport() {
      this.importDialogVisible = true
    },
    handleEdit(e) {
      this.type = 1
      this.dialogVisible = true
      this.form = JSON.parse(JSON.stringify(e))
      this.form.password = '******'
    },
    handleResetPassword(e) {
      resetPassword({
        id: e.id
      }).then((res) => {
        this.$message.success('重置密码成功')
      })
    },
    handleDel(e) {
      this.delUserId = e.id
      this.delDialogVisible = true
    },
    doDel() {
      del(this.delUserId).then((res) => {
        this.delDialogVisible = false
        if (this.pageNum > 1 && this.tableData.length === 1) {
          this.pageNum--
        }
        this.getList()
        this.$message.success('删除成功')
      })
    },
    // 下载模板
    downloadTemplate() {
      download().then((res) => {
        const url = window.URL.createObjectURL(
          new Blob([res.data], {
            type: 'application/vnd.ms-excel;charset=UTF-8'
          })
        )
        const temp = res.headers['content-disposition']
          .split(';')[1]
          .split('filename=')[1]
        const index = temp.indexOf('.')
        const str = temp.substr(0, index)
        const fileName = `${utf8to16(unescape(str))}.xlsx`
        const link = document.createElement('a')
        link.href = url
        link.download = fileName
        link.click()
        this.$message({
          type: 'success',
          message: '下载成功'
        })
      })
    },
    // 导入
    handleImportSubmit(file) {
      const blob = file.raw
      const type = blob.name.split('.')[1]
      if (type !== 'xlsx') {
        this.$message.warning(
          '导入文件只能是.xlsx结尾的文件，请检查上传文件是否是从本页面下载的模板'
        )
      } else {
        const formData = new FormData()
        formData.append('file', blob)
        console.log(file, formData, 'formData')
        importExcel(formData)
          .then((res) => {
            this.importDialogVisible = false
            this.resultData = res.data
            this.dialogVisibleImport = true
          })
      }
    },
    // 导出
    bacthExport() {
      bacthExport().then((res) => {
        const url = window.URL.createObjectURL(
          new Blob([res.data], {
            type: 'application/vnd.ms-excel;charset=UTF-8'
          })
        )
        const temp = res.headers['content-disposition']
          .split(';')[1]
          .split('filename=')[1]
        const index = temp.indexOf('.')
        const str = temp.substr(0, index)
        const fileName = `${utf8to16(unescape(str))}.xlsx`
        const link = document.createElement('a')
        link.href = url
        link.download = fileName
        link.click()
        this.$message({
          type: 'success',
          message: '导出成功'
        })
      })
    },
    handleCloseImport() {
      this.getList()
      this.dialogVisibleImport = false
    },
    submitFormImport() {
      this.getList()
      this.dialogVisibleImport = false
    },
    // 下载失败数据
    downFail() {
      const oReq = new XMLHttpRequest()
      oReq.open('GET', this.resultData.failExcelUrl, true)
      oReq.responseType = 'arraybuffer'
      oReq.withCredentials = true
      // eslint-disable-next-line func-names
      oReq.onload = function() {
        const arraybuffer = oReq.response
        if (arraybuffer) {
          const byteBUffer = new Uint8Array(arraybuffer)
          const link = document.createElement('a')
          const blob = new Blob([byteBUffer], {
            type: 'application/vnd.ms-excel;charset=UTF-8'
          })
          link.href = URL.createObjectURL(blob)
          link.download = '导入失败数据.xlsx'
          link.click()
        }
      }
      oReq.send()
      this.$message({
        type: 'success',
        message: '下载成功'
      })
    },
    // 按钮点击事件
    handleClick(tagName, e) {
      switch (tagName) {
        case 'new':
          this.handleNew()
          break
        case 'import':
          this.handleImport()
          break
        case 'edit':
          this.handleEdit(e)
          break
        case 'resetPassword':
          this.handleResetPassword(e)
          break
        case 'del':
          this.handleDel(e)
          break
        default:
          break
      }
    },
    // 查看电话
    getShowPhone(e, index) {
      console.log(e, index)
      getPhone(e.id).then((res) => {
        // this.$message.success(res.data)\
        if (res.data && res.data.phone) {
          this.tableData[index].phoneNew = res.data.phone
          this.tableData[index].showPhone = true
        } else {
          this.tableData[index].showPhone = false
        }
      }).catch(() => {
        // this.$message.error('获取手机号失败')
        this.tableData[index].showPhone = false
      })
    }
  }

}
</script>

<style lang="scss" scoped>
.main-content {
  padding: 30px 0;
  box-sizing: border-box;
  font-family: PingFang SC RE;

  .top {
    display: flex;
    margin-bottom: 20px;
    .left {
      display: flex;
      align-items: center;
      font-size: 17px;
      font-weight: bold;
      .pa_name {
        color: #8D95A5;
        margin-right: 5px;
      }
      .ch_name {
        color: #202225;
        margin-right: 5px;
      }
    }
  }

  .line {
    width: 100%;
    height: 1px;
    background: #E5E5E5;
    margin-bottom: 30px;
  }

  .content-table {
    width: 100%;
    height: 94%;
    position: relative;

    .title {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;

      .input_style {
        margin-right: 20px;
        width: 200px;
      }

      .title_name {
        height: 20px;
        font-size: 18px;
        font-family: PingFang SC RE;
        font-weight: bold;
        color: #202225;
        padding-left: 10px;
        border-left: 4px solid #1768EB;
      }
    }
  }

  .dialog_footer {
    display: flex;
    justify-content: center;
    // margin-top: 20px;
  }
}

.uploadText1 {
  display: flex;
  align-items: center;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 15px;
  color: #202225;
}

.uploadText2 {
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 13px;
  color: #6A6C74;
}

::v-deep .el-upload__tip {
  margin-top: 0;
}
</style>
