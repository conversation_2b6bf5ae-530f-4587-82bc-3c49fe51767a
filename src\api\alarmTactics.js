import request from '@/utils/request'

// 设置告警策略
export function setTactics(data) {
  return request({
    url: `/api/v1/alarm-strategy/audio`,
    method: 'post',
    data,
  })
}

// 获取告警策略
export function getTactics() {
  return request({
    url: `/api/v1/alarm-strategy/audio`,
    method: 'get',
  })
}

// 上传报警音频
export function uploadAudio(data) {
  return request({
    url: `/api/v1/alarm-strategy/uploadAudio`,
    method: 'post',
    data,
  })
}

// 告警策略等级列表
export function getAlarmLevelList() {
  return request({
    url: '/api/v1/alarm-strategy/list',
    method: 'get',
  })
}

// 保存告警策略
export function saveTactics(data) {
  return request({
    url: '/api/v1/alarm-strategy',
    method: 'put',
    data,
  })
}
