Index: src/views/aobo/fieldView/threeJs/index.vue
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><template>\r\n  <div class=\"threeOut\">\r\n    <div ref=\"container\" class=\"threeJs_container\" />\r\n\r\n    <transition appear name=\"fade\">\r\n      <div v-if=\"!threeJsLoaded\" class=\"loading-text\">{{ Math.floor(threeJsProgress) }}%</div>\r\n    </transition>\r\n\r\n    <!-- 实时曲线图表数据 -->\r\n    <el-dialog v-if=\"dialogVisible\" title=\"详情\" :visible.sync=\"dialogVisible\" width=\"60vw\" append-to-body destroy-on-close top=\"90px\">\r\n      <MarkerDetail :temp-level-list=\"tempLevelList\" :data=\"formData\" />\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { debounce } from '@/utils'\r\nimport { getAlarmLevelList } from '@/api/alarmTactics'\r\nimport { camera, Core, css2dRenderer, environment, orbitControls, postProcess, renderer, scene } from '@/views/aobo/fieldView/threeJs/service/core'\r\nimport { RayCasterController } from '@/views/aobo/fieldView/threeJs/service/rayCasterController'\r\nimport { mapGetters } from 'vuex'\r\nimport Stats from 'three/examples/jsm/libs/stats.module'\r\nimport * as THREE from 'three'\r\nimport gsap from 'gsap'\r\nimport Vue from 'vue'\r\nimport { CSS2DObject } from 'three/examples/jsm/renderers/CSS2DRenderer'\r\nimport MarkerMarkerDetail from '@/views/aobo/fieldView/threeJs/components/MarkerDetail.vue'\r\nimport SegmentPopup from '../components/SegmentPopup.vue'\r\nimport TubePopup from '../components/TubePopup.vue'\r\nimport AlarmMarkerPopup from '../components/AlarmMarkerPopup.vue'\r\n\r\nconst alarmColorMap = {\r\n  0: '#00A1FF',\r\n  1: '#FF7F00',\r\n  2: '#FFFF00',\r\n  3: '#FF0000',\r\n}\r\n\r\n// 每隔多少米一个点\r\nconst segmentLength = 1\r\nlet count = 0\r\n\r\nexport default {\r\n  name: 'LineMap',\r\n  components: { MarkerDetail: MarkerMarkerDetail, },\r\n  props: {\r\n    pipelineList: {\r\n      type: Array,\r\n      default: () => [],\r\n    },\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      core: new Core(),\r\n      rayCasterController: null,\r\n      debouncedResizeHandler: null,\r\n\r\n      // 所有分段管道\r\n      tubes: [],\r\n      // 存储动画ID，用于清理\r\n      segmentAnimationIds: [],\r\n      alarmAnimationId: null,\r\n\r\n      // 选中告警点位的扩散效果\r\n      sphereExpansion: null,\r\n\r\n      // InstancedMesh 相关\r\n      alarmInstancedMesh: null,\r\n      alarmInstanceData: [], // 存储每个实例的数据\r\n      alarmGeometry: null,\r\n      alarmMaterial: null,\r\n      maxAlarmCount: 10000, // 最大告警点位数量\r\n\r\n      // 创建变换矩阵\r\n      dummy: new THREE.Object3D(),\r\n\r\n      offSegmentClick: () => {},\r\n      offTubeClick: () => {},\r\n      offAlarmClick: () => {},\r\n\r\n      dialogVisible: false,\r\n      dialogVisibleMarker: false,\r\n      formData: null,\r\n      loading: false,\r\n      currentPopup: null, // 当前显示的弹窗\r\n      alarmLevelList: [],\r\n      tempLevelList: [],\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters(['threeJsLoaded', 'threeJsProgress']),\r\n  },\r\n\r\n  watch: {\r\n    pipelineList() {\r\n      if (this.threeJsLoaded) {\r\n        this.drawSegments()\r\n      }\r\n    },\r\n    threeJsLoaded(newVal) {\r\n      if (!newVal) return\r\n      this.offTubeClick = this.rayCasterController.bindClickRayCastObj(environment.tubeList, this.onTubeClick)\r\n      if (this.pipelineList?.length) {\r\n        this.drawSegments()\r\n      }\r\n    },\r\n  },\r\n  created() {\r\n    this.getAlarmLevelList()\r\n    postProcess.init()\r\n  },\r\n  mounted() {\r\n    const containerRef = this.$refs.container\r\n    containerRef.appendChild(renderer.domElement)\r\n    containerRef.appendChild(css2dRenderer.domElement)\r\n    this.onResize()\r\n    this.rayCasterController = new RayCasterController(camera, this.$refs.container)\r\n\r\n    // const stats = new Stats()\r\n    // const statsDom = stats.domElement\r\n    // statsDom.classList.add('stats')\r\n    // document.body.appendChild(statsDom)\r\n    //\r\n    // const updateState = () => {\r\n    //   stats.update()\r\n    //\r\n    //   requestAnimationFrame(() => {\r\n    //     updateState()\r\n    //   })\r\n    // }\r\n    //\r\n    // requestAnimationFrame(updateState)\r\n  },\r\n  beforeDestroy() {\r\n    this.closeCurrentPopup()\r\n    this.clearSegments()\r\n    this.offTubeClick()\r\n    this.core.dispose()\r\n    window.removeEventListener('resize', this.debouncedResizeHandler)\r\n  },\r\n\r\n  methods: {\r\n    // 窗口大小变化后重新设置threeJs画布尺寸\r\n    onResize() {\r\n      const containerRef = this.$refs.container\r\n      const resizeHandler = () => {\r\n        const { width, height } = containerRef.getBoundingClientRect()\r\n        this.core.changeSize(width, height)\r\n      }\r\n      resizeHandler()\r\n      this.debouncedResizeHandler = debounce(resizeHandler, 100, false)\r\n      window.addEventListener('resize', this.debouncedResizeHandler)\r\n    },\r\n\r\n    /**\r\n     * 获取等级列表\r\n     * */\r\n    getAlarmLevelList() {\r\n      getAlarmLevelList()\r\n        .then((res) => {\r\n          this.alarmLevelList = res.data\r\n          // 根据value排序，大的在前\r\n          this.tempLevelList = res.data[0].levels.sort((a, b) => b.value - a.value)\r\n        })\r\n    },\r\n\r\n    /**\r\n     * 初始化告警点位的 InstancedMesh\r\n     */\r\n    initAlarmInstancedMesh() {\r\n      // 创建共享几何体\r\n      this.alarmGeometry = new THREE.SphereGeometry(0.15, 32, 16)\r\n      this.alarmGeometry.computeBoundsTree()\r\n\r\n      // 创建实心球体材质\r\n      this.alarmMaterial = new THREE.MeshPhongMaterial({\r\n        transparent: false,\r\n        side: THREE.DoubleSide,\r\n      })\r\n\r\n      // 创建 InstancedMesh\r\n      this.alarmInstancedMesh = new THREE.InstancedMesh(this.alarmGeometry, this.alarmMaterial, this.maxAlarmCount)\r\n      this.alarmInstancedMesh.count = 0\r\n\r\n      // 添加到场景\r\n      scene.add(this.alarmInstancedMesh)\r\n    },\r\n\r\n    /**\r\n     * 清理管道、材质和动画\r\n     */\r\n    clearSegments() {\r\n      // 清理事件监听\r\n      this.offSegmentClick()\r\n      this.offAlarmClick()\r\n      // 清理动画\r\n      this.segmentAnimationIds.forEach((id) => {\r\n        if (id) {\r\n          cancelAnimationFrame(id)\r\n        }\r\n      })\r\n      this.segmentAnimationIds = []\r\n\r\n      if (this.sphereExpansion) {\r\n        cancelAnimationFrame(this.alarmAnimationId)\r\n        this.alarmAnimationId = null\r\n\r\n        scene.remove(this.sphereExpansion)\r\n        this.sphereExpansion.geometry.dispose()\r\n        this.sphereExpansion.material.dispose()\r\n        this.sphereExpansion = null\r\n      }\r\n\r\n      // 清理管道\r\n      this.tubes.forEach((tube) => {\r\n        // 从场景中移除\r\n        scene.remove(tube)\r\n\r\n        // 清理几何体\r\n        if (tube.geometry) {\r\n          tube.geometry.dispose()\r\n        }\r\n\r\n        // 清理材质\r\n        if (tube.material) {\r\n          if (tube.material.map) {\r\n            tube.material.map.dispose()\r\n          }\r\n          tube.material.dispose()\r\n        }\r\n\r\n        // 清理用户数据\r\n        tube.userData = null\r\n      })\r\n      this.tubes = []\r\n\r\n      // 清理 InstancedMesh\r\n      if (this.alarmInstancedMesh) {\r\n        this.alarmInstancedMesh.dispose()\r\n        scene.remove(this.alarmInstancedMesh)\r\n        this.alarmInstancedMesh = null\r\n      }\r\n      if (this.alarmGeometry) {\r\n        this.alarmGeometry.dispose()\r\n        this.alarmGeometry = null\r\n      }\r\n      if (this.alarmMaterial) {\r\n        this.alarmMaterial.dispose()\r\n        this.alarmMaterial = null\r\n      }\r\n      this.alarmInstanceData = []\r\n\r\n      // 清理后处理选择对象\r\n      if (postProcess.bloomPass?.selection) {\r\n        postProcess.bloomPass.selection.clear()\r\n        postProcess.outlinePass.selection.clear()\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 绘制电缆分段\r\n     * */\r\n    drawSegments() {\r\n      // 先清理之前的管道\r\n      this.clearSegments()\r\n      count = 0\r\n\r\n      // 初始化告警点位的 InstancedMesh\r\n      this.initAlarmInstancedMesh()\r\n\r\n      for (const item of this.pipelineList) {\r\n        if (!item.segments?.length) continue\r\n        const curve = environment.curveMap[item.code]\r\n        if (!curve) {\r\n          this.$message.warning(`未找到对应的曲线: ${item.code}`)\r\n          continue\r\n        }\r\n\r\n        const totalLength = curve.getLength()\r\n        for (const segment of item.segments) {\r\n          if (segment.startPosition / 100 >= totalLength) continue\r\n\r\n          const startDistance = segment.startPosition / 100\r\n          const endDistance = Math.min(segment.endPosition / 100, totalLength)\r\n          const segmentT = segmentLength / totalLength\r\n          const pointCount = Math.max(Math.floor((endDistance - startDistance) / segmentLength) - 1, 0)\r\n          const startT = startDistance / totalLength\r\n          const segmentPoints = [curve.getPoint(startT)]\r\n          for (let i = 1; i <= pointCount; i++) {\r\n            const t = startT + i * segmentT\r\n            segmentPoints.push(curve.getPoint(t))\r\n          }\r\n\r\n          segmentPoints.push(curve.getPoint(endDistance / totalLength))\r\n          const segmentCurve = new THREE.CatmullRomCurve3(segmentPoints)\r\n          const segmentTubeGeometry = new THREE.TubeGeometry(segmentCurve, pointCount + 1, 0.12, 12, false)\r\n          segmentTubeGeometry.computeBoundsTree()\r\n\r\n          // 创建管道材质，使用加载的纹理\r\n          const segmentTubeMaterial = new THREE.MeshLambertMaterial({\r\n            map: segment.alarmLevel === null ? environment.textureObj.status[0] : environment.textureObj.status[segment.alarmLevel],\r\n            side: THREE.DoubleSide,\r\n            opacity: 0.8, // 设置透明度，如果需要半透明效果\r\n            transparent: true, // 启用透明度\r\n\r\n            // depthWrite: false, // 禁用深度写入以实现透明效果\r\n            // depthTest: false, // 启用深度测试以确保正确渲染\r\n          })\r\n\r\n          // const segmentTubeMaterial = new THREE.MeshPhysicalMaterial({\r\n          //   map: segment.alarmLevel === null ? environment.textureObj.status[0] : environment.textureObj.status[segment.alarmLevel],\r\n          //   transmission: 0.5, // 设置透射度，1.0表示完全透明\r\n          //   ior: 1.5, // 设置折射率，玻璃的典型值\r\n          //   roughness: 0.8, // 设置粗糙度，0.0表示完全光滑\r\n          //   metalness: 0.5, // 设置金属度，0.0表示非金属\r\n          //   color: 0xffffff, // 设置颜色，可以根据需要调整\r\n          //   side: THREE.DoubleSide,\r\n          //   // opacity: 0.5, // 设置透明度，如果需要半透明效果\r\n          //   transparent: true // 启用透明度\r\n          // })\r\n\r\n          const segmentTube = new THREE.Mesh(segmentTubeGeometry, segmentTubeMaterial)\r\n          segmentTube.name = item.name\r\n          segment.parentCube = item.name\r\n          segmentTube.userData.data = segment\r\n          segmentTube.userData.code = item.code\r\n          // tube.visible = false\r\n          scene.add(segmentTube)\r\n          this.tubes.push(segmentTube)\r\n\r\n          // 添加告警点位\r\n          if (segment.alarms?.length) {\r\n            for (const alarm of segment.alarms) {\r\n              alarm.parentData = segment\r\n              this.drawAlarmMarker(curve, alarm)\r\n            }\r\n          }\r\n\r\n          // 如果告警添加闪烁动画\r\n          if (segment.alarmLevel !== null) {\r\n            postProcess.outlinePass.selection.add(segmentTube)\r\n            const animationIndex = this.segmentAnimationIds.length\r\n            this.segmentAnimationIds.push(null) // 先占位\r\n\r\n            const animate = () => {\r\n              const intensity = (Math.sin(Date.now() * 0.01) + 1) / 2\r\n              segmentTubeMaterial.emissive.setRGB(0.5 * intensity, 0, 0)\r\n              segmentTubeMaterial.opacity = 0.6 + 0.4 * intensity // 透明度从0.6到1.0之间变化\r\n              this.segmentAnimationIds[animationIndex] = requestAnimationFrame(animate)\r\n            }\r\n\r\n            animate()\r\n          }\r\n        }\r\n      }\r\n\r\n      this.offSegmentClick = this.rayCasterController.bindClickRayCastObj(this.tubes, this.onSegmentClick)\r\n      this.offAlarmClick = this.rayCasterController.bindClickRayCastObj([this.alarmInstancedMesh], this.onAlarmClick)\r\n      console.log(count, '告警点总数')\r\n    },\r\n\r\n    /**\r\n     * 绘制告警点位\r\n     * */\r\n    drawAlarmMarker(curve, data) {\r\n      count++\r\n\r\n      const t = Math.min(data.cablePosition / (curve.getLength() * 100), 1)\r\n      const alarmPoint = curve.getPoint(t)\r\n\r\n      // 添加到实例数据数组\r\n      const instanceIndex = this.alarmInstanceData.length\r\n      this.alarmInstanceData.push({\r\n        position: alarmPoint,\r\n        data,\r\n        instanceIndex,\r\n      })\r\n\r\n      this.dummy.scale.set(1, 1, 1)\r\n      this.dummy.position.copy(alarmPoint)\r\n      this.dummy.updateMatrix()\r\n\r\n      // 更新实心球体实例\r\n      this.alarmInstancedMesh.setMatrixAt(instanceIndex, this.dummy.matrix)\r\n      this.alarmInstancedMesh.setColorAt(instanceIndex, new THREE.Color(alarmColorMap[data.level]))\r\n      this.alarmInstancedMesh.count = this.alarmInstanceData.length\r\n\r\n      // 标记需要更新\r\n      this.alarmInstancedMesh.instanceMatrix.needsUpdate = true\r\n      this.alarmInstancedMesh.instanceColor.needsUpdate = true\r\n\r\n      // 将数据存储到 userData 中以供点击检测使用\r\n      if (!this.alarmInstancedMesh.userData.instanceData) {\r\n        this.alarmInstancedMesh.userData.instanceData = []\r\n      }\r\n      this.alarmInstancedMesh.userData.instanceData[instanceIndex] = data\r\n    },\r\n\r\n    /**\r\n     * 点击告警点位\r\n     * */\r\n    onAlarmClick(obj, _, instanceId) {\r\n      // 先关闭上一个弹窗\r\n      this.closeCurrentPopup()\r\n\r\n      const data = obj.userData.instanceData[instanceId]\r\n\r\n      // 告警点中心点\r\n      const position = this.alarmInstanceData[instanceId].position.clone()\r\n\r\n      const popup = this.addPopup(AlarmMarkerPopup, position, data)\r\n      popup.vueInstance.$on('detail', (row) => {\r\n        this.openDetail(row.parentData)\r\n      })\r\n\r\n      const { t } = this.getClosestPointOnCurve(environment.centerCurve, position)\r\n      const positive = t >= 0.8 ? -1 : 1\r\n      const cameraPosition = environment.centerCurve.getPoint(t + 0.002 * positive)\r\n      gsap.to(orbitControls.target, {\r\n        x: position.x,\r\n        y: position.y,\r\n        z: position.z,\r\n        duration: 0.5,\r\n        ease: 'none',\r\n        onUpdate: () => {\r\n          // orbitControls.update()\r\n        },\r\n      })\r\n      gsap.to(camera.position, {\r\n        x: cameraPosition.x,\r\n        y: cameraPosition.y,\r\n        z: cameraPosition.z,\r\n        duration: 0.5,\r\n        ease: 'none',\r\n      })\r\n\r\n      /**\r\n       *  在告警中心点添加一个扩散效果\r\n       * */\r\n      const sphereGeometry = new THREE.SphereGeometry(0.15, 32, 16)\r\n      sphereGeometry.computeBoundsTree()\r\n\r\n      // 创建动画球体材质\r\n      const sphereMaterial = new THREE.MeshPhongMaterial({\r\n        transparent: true,\r\n        color: new THREE.Color(alarmColorMap[data.level]),\r\n        opacity: 0.8,\r\n        depthWrite: false,\r\n        depthTest: false,\r\n      })\r\n      const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial)\r\n      this.sphereExpansion = sphere\r\n      sphere.position.copy(position)\r\n      scene.add(sphere)\r\n      const animationStartTime = Date.now()\r\n      const animationDuration = 1000 // 1秒一个周期\r\n      const maxScale = 3 // 最大缩放倍数\r\n      const maxOpacity = 0.8 // 最大透明度（开始时）\r\n      const minOpacity = 0 // 最小透明度（结束时）\r\n\r\n      const animateExpansion = () => {\r\n        cancelAnimationFrame(this.alarmAnimationId)\r\n        const elapsed = Date.now() - animationStartTime\r\n        const progress = (elapsed % animationDuration) / animationDuration\r\n\r\n        // 使用缓动函数让动画更自然\r\n        const easeProgress = progress // 线性进度，从0到1\r\n\r\n        // 计算当前缩放比例（从1到maxScale）\r\n        const currentScale = 1 + (maxScale - 1) * easeProgress\r\n        sphere.scale.set(currentScale, currentScale, currentScale)\r\n\r\n        // 计算当前透明度（从maxOpacity到minOpacity）\r\n        const currentOpacity = maxOpacity - (maxOpacity - minOpacity) * easeProgress\r\n        sphereMaterial.opacity = currentOpacity\r\n\r\n        this.alarmAnimationId = requestAnimationFrame(animateExpansion)\r\n      }\r\n\r\n      animateExpansion()\r\n    },\r\n\r\n    /**\r\n     * 关闭当前弹窗\r\n     * */\r\n    closeCurrentPopup() {\r\n      if (this.currentPopup) {\r\n        scene.remove(this.currentPopup)\r\n        this.currentPopup.vueInstance.$destroy()\r\n        this.currentPopup.element?.remove()\r\n        this.currentPopup = null\r\n        if (this.sphereExpansion) {\r\n          cancelAnimationFrame(this.alarmAnimationId)\r\n          this.alarmAnimationId = null\r\n\r\n          scene.remove(this.sphereExpansion)\r\n          this.sphereExpansion.geometry.dispose()\r\n          this.sphereExpansion.material.dispose()\r\n          this.sphereExpansion = null\r\n        }\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 添加弹窗\r\n     * */\r\n    addPopup(component, position, data) {\r\n      const CompConstructor = Vue.extend(component)\r\n      const div = document.createElement('div')\r\n      const instance = new CompConstructor({\r\n        el: div,\r\n        propsData: { data },\r\n      })\r\n\r\n      // 监听组件内部emit的close事件\r\n      instance.$on('close', () => {\r\n        this.closeCurrentPopup()\r\n      })\r\n\r\n      const popupEl = instance.$el\r\n      const popup = new CSS2DObject(popupEl)\r\n      const popupPosition = position.clone()\r\n      popup.position.copy(popupPosition)\r\n\r\n      // 给弹窗注册点击事件\r\n      popupEl.addEventListener('mousedown', (e) => {\r\n        // 仅针对鼠标左键点击\r\n        if (e.button !== 0) return\r\n        e.stopPropagation()\r\n      })\r\n\r\n      scene.add(popup)\r\n\r\n      // 保存 Vue 组件实例引用，用于正确销毁\r\n      popup.vueInstance = instance\r\n\r\n      // 设置为当前弹窗\r\n      this.currentPopup = popup\r\n\r\n      return popup\r\n    },\r\n\r\n    /**\r\n     * 点击管道显示管道详情\r\n     * */\r\n    onTubeClick(obj, point) {\r\n      const data = this.pipelineList.find((item) => item.code === obj.name)\r\n      if (!data) {\r\n        this.$message.warning('未找到对应的管道数据')\r\n        return\r\n      }\r\n\r\n      // postProcess.bloomPass.selection.set([obj])\r\n\r\n      // 聚焦到管道上\r\n      const position = point.clone()\r\n      // 先关闭上一个弹窗\r\n      this.closeCurrentPopup()\r\n\r\n      const { t } = this.getClosestPointOnCurve(environment.centerCurve, point)\r\n\r\n      const segmentCurve = environment.curveMap[data.code]\r\n      const pointInfo = this.getClosestPointOnCurve(segmentCurve, point)\r\n      const length = segmentCurve.getLength() * pointInfo.t\r\n      data.pointPosition = length\r\n      this.addPopup(TubePopup, point, data)\r\n\r\n      const positive = t >= 0.8 ? -1 : 1\r\n      const cameraPosition = environment.centerCurve.getPoint(t + 0.002 * positive)\r\n      gsap.to(orbitControls.target, {\r\n        x: position.x,\r\n        y: position.y,\r\n        z: position.z,\r\n        duration: 0.5,\r\n        ease: 'none',\r\n        onUpdate: () => {\r\n          // orbitControls.update()\r\n        },\r\n      })\r\n      gsap.to(camera.position, {\r\n        x: cameraPosition.x,\r\n        y: cameraPosition.y,\r\n        z: cameraPosition.z,\r\n        duration: 0.5,\r\n        ease: 'none',\r\n      })\r\n    },\r\n\r\n    /**\r\n     * 点击分段事件\r\n     * */\r\n    onSegmentClick(obj, point) {\r\n      // postProcess.bloomPass.selection.set([obj])\r\n      // 先关闭上一个弹窗\r\n      this.closeCurrentPopup()\r\n\r\n      const { data, code } = obj.userData\r\n      // 聚焦到管道上\r\n      const position = point.clone()\r\n\r\n      const { t } = this.getClosestPointOnCurve(environment.centerCurve, point)\r\n      const segmentCurve = environment.curveMap[code]\r\n      const pointInfo = this.getClosestPointOnCurve(segmentCurve, point)\r\n      const length = segmentCurve.getLength() * pointInfo.t\r\n      data.pointPosition = length\r\n      this.addPopup(SegmentPopup, point, data)\r\n\r\n      const positive = t >= 0.8 ? -1 : 1\r\n      const cameraPosition = environment.centerCurve.getPoint(t + 0.002 * positive)\r\n      gsap.to(orbitControls.target, {\r\n        x: position.x,\r\n        y: position.y,\r\n        z: position.z,\r\n        duration: 0.5,\r\n        ease: 'none',\r\n        onUpdate: () => {\r\n          // orbitControls.update()\r\n        },\r\n      })\r\n      gsap.to(camera.position, {\r\n        x: cameraPosition.x,\r\n        y: cameraPosition.y,\r\n        z: cameraPosition.z,\r\n        duration: 0.5,\r\n        ease: 'none',\r\n      })\r\n    },\r\n\r\n    /**\r\n     * 获取曲线上距离指定点最近的点\r\n     * @param {THREE.Curve} curve - Three.js曲线对象\r\n     * @param {THREE.Vector3} targetPoint - 目标点\r\n     */\r\n    getClosestPointOnCurve(curve, targetPoint) {\r\n      // 使用二分法搜索最近点\r\n      let left = 0\r\n      let right = 1\r\n      let bestT = 0.5\r\n      let bestDistance = Infinity\r\n\r\n      // 三分搜索法\r\n      for (let iteration = 0; iteration < 29; iteration++) {\r\n        const t1 = left + (right - left) / 3\r\n        const t2 = right - (right - left) / 3\r\n\r\n        const point1 = curve.getPoint(t1)\r\n        const point2 = curve.getPoint(t2)\r\n\r\n        const dist1 = point1.distanceTo(targetPoint)\r\n        const dist2 = point2.distanceTo(targetPoint)\r\n\r\n        if (dist1 < dist2) {\r\n          right = t2\r\n          if (dist1 < bestDistance) {\r\n            bestDistance = dist1\r\n            bestT = t1\r\n          }\r\n        } else {\r\n          left = t1\r\n          if (dist2 < bestDistance) {\r\n            bestDistance = dist2\r\n            bestT = t2\r\n          }\r\n        }\r\n      }\r\n\r\n      return {\r\n        point: curve.getPoint(bestT),\r\n        t: bestT,\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 点击线打开详情\r\n     * */\r\n    openDetail(row) {\r\n      this.formData = { ...row }\r\n      this.dialogVisible = true\r\n    },\r\n\r\n    /**\r\n     * 供外部调用，定位到具体的告警点\r\n     * */\r\n    goToAlarmMarker(id) {\r\n      for (const item of this.alarmInstanceData) {\r\n        if (item.data.id === id) {\r\n          this.onAlarmClick(this.alarmInstancedMesh, item.position, item.instanceIndex)\r\n          return\r\n        }\r\n      }\r\n\r\n      this.$message.warning('未找到对应的告警点')\r\n    },\r\n\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.threeOut {\r\n  position: relative;\r\n  height: 100%;\r\n}\r\n.threeJs_container {\r\n  height: 100%;\r\n  position: absolute;\r\n  inset: 0;\r\n  z-index: 9;\r\n}\r\n\r\n.loading-text {\r\n  position: absolute;\r\n  inset: 0;\r\n  display: grid;\r\n  place-items: center;\r\n  font-size: 80px;\r\n  z-index: 999;\r\n  user-select: none;\r\n  color: #bdbcbc;\r\n  text-shadow: 0 1px 0 hsl(174, 5%, 80%), 0 2px 0 hsl(174, 5%, 75%), 0 3px 0 hsl(174, 5%, 70%), 0 4px 0 hsl(174, 5%, 66%), 0 5px 0 hsl(174, 5%, 64%),\r\n    0 6px 0 hsl(174, 5%, 62%), 0 7px 0 hsl(174, 5%, 61%), 0 8px 0 hsl(174, 5%, 60%), 0 0 5px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.2),\r\n    0 3px 5px rgba(0, 0, 0, 0.2), 0 5px 10px rgba(0, 0, 0, 0.2), 0 10px 10px rgba(0, 0, 0, 0.2), 0 20px 20px rgba(0, 0, 0, 0.3);\r\n}\r\n</style>\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/views/aobo/fieldView/threeJs/index.vue b/src/views/aobo/fieldView/threeJs/index.vue
--- a/src/views/aobo/fieldView/threeJs/index.vue	(revision 2dc6d68a6118a4994fdfcca9ac577843f012d219)
+++ b/src/views/aobo/fieldView/threeJs/index.vue	(date 1756362837256)
@@ -163,6 +163,7 @@
           this.alarmLevelList = res.data
           // 根据value排序，大的在前
           this.tempLevelList = res.data[0].levels.sort((a, b) => b.value - a.value)
+          console.log(this.tempLevelList, '===================')
         })
     },
 
@@ -412,7 +413,7 @@
 
       const popup = this.addPopup(AlarmMarkerPopup, position, data)
       popup.vueInstance.$on('detail', (row) => {
-        this.openDetail(row.parentData)
+        this.openDetail(row)
       })
 
       const { t } = this.getClosestPointOnCurve(environment.centerCurve, position)
Index: src/views/aobo/fieldView/threeJs/components/MarkerDetail.vue
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><template>\r\n  <LineCharts ref=\"chartRef\" v-loading=\"loading\" />\r\n</template>\r\n\r\n<script>\r\nimport Vue from 'vue'\r\nimport LineCharts from '@/views/aobo/curveView/modules/lineCharts.vue'\r\nimport { liveTrend } from '@/api/lineCharts'\r\nimport * as echarts from 'echarts'\r\n\r\nconst alarmColorMap = {\r\n  0: '#0000FF',\r\n  1: '#FFA500',\r\n  2: '#e4e46f',\r\n  3: '#FF0000',\r\n}\r\n\r\nexport default Vue.extend({\r\n  name: 'MarkerDetail',\r\n  components: { LineCharts },\r\n  props: {\r\n    data: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    tempLevelList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      loading: true,\r\n      colorList: [\r\n        { color1: '#5470c6', color2: 'rgba(84, 112, 198, 0)' },\r\n        { color1: '#91cc75', color2: 'rgba(145, 204, 117, 0)' },\r\n        { color1: '#fac858', color2: 'rgba(250, 200, 88, 0)' },\r\n        { color1: '#ee6666', color2: 'rgba(238, 102, 102, 0)' },\r\n        { color1: '#73c0de', color2: 'rgba(115, 192, 222, 0)' },\r\n        { color1: '#3ba272', color2: 'rgba(59, 162, 114, 0)' },\r\n        { color1: '#fc8452', color2: 'rgba(252, 132, 82, 0)' },\r\n        { color1: '#9a60b4', color2: 'rgba(154, 96, 180, 0)' },\r\n        { color1: '#ea7ccc', color2: 'rgba(234, 124, 204, 0)' },\r\n      ],\r\n\r\n    }\r\n  },\r\n  created() {\r\n    this.getChartsData()\r\n  },\r\n\r\n  methods: {\r\n    /**\r\n     * 根据温度判断报警等级\r\n     * */\r\n    getAlarmLevel(temp) {\r\n      if (!this.tempLevelList.length) return\r\n      for (const item of this.tempLevelList) {\r\n        if (temp >= item.value) {\r\n          return item.level\r\n        }\r\n      }\r\n    },\r\n\r\n    getChartsData() {\r\n      const data = {\r\n        segmentIds: [this.data.id],\r\n        date: this.date,\r\n        min: this.data.startPosition,\r\n        max: this.data.endPosition\r\n      }\r\n      this.loading = true\r\n      // this.$refs.chartRef?.chart?.clear()\r\n      liveTrend(data).then((res) => {\r\n        const chartsData = {\r\n          xAxisData: [],\r\n          seriesData: []\r\n        }\r\n        let lineChartData = []\r\n        if (!res.data || !res.data.segments.length) {\r\n          return\r\n        }\r\n        const { segments } = res.data\r\n        lineChartData = segments\r\n        // 判断哪一项有数据,如果都没有temperatures数据，则为暂无数据\r\n        const haveDataArray = lineChartData.find((item) => (item.temperatures || []).length)\r\n        if (haveDataArray) {\r\n          chartsData.xAxisData = haveDataArray.temperatures.map((item) => item.position)\r\n        } else {\r\n          return\r\n        }\r\n        lineChartData.forEach((item) => {\r\n          item.temperatures.forEach((item1) => {\r\n            item1.fiberId = item.fiberId\r\n            item1.startPosition = item.startPosition\r\n          })\r\n        })\r\n\r\n        chartsData.seriesData = lineChartData.map((item, index) => {\r\n          const datas = (item.temperatures || []).map((item1) => {\r\n            item1.value = item1.temperature\r\n            const alarmLevel = this.getAlarmLevel(item1.value)\r\n            console.log(item1.value, alarmLevel, 'alarmLevel')\r\n            this.$set(item1, 'itemStyle', {\r\n              color: alarmColorMap[alarmLevel] || '#00E667'\r\n            })\r\n            this.$set(item1, 'itemStyle', {\r\n              color: alarmColorMap[alarmLevel] || '#00E667',\r\n              opacity: alarmLevel !== undefined ? 1 : 0\r\n            })\r\n            return [item1.position, (item1.value / 100).toFixed(2), item1]\r\n          })\r\n          const that = this\r\n          return {\r\n            name: `${item.name}`,\r\n            type: 'line',\r\n            data: datas,\r\n            smooth: true, // 平滑曲线\r\n            sampling: 'average',\r\n            large: true,\r\n            symbolSize(params) {\r\n              return that.getAlarmLevel(params[1]) !== undefined ? 8 : 0\r\n            },\r\n            itemStyle: {\r\n              normal: {\r\n                color(params) {\r\n                  // params 是当前数据点的信息\r\n                  return alarmColorMap[that.getAlarmLevel(params.value[1])] || that.colorList[index].color1\r\n                }\r\n              }\r\n            },\r\n            lineStyle: { width: 2.5, color: this.colorList[index].color1 },\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: this.colorList[index].color2 },\r\n                { offset: 0.9, color: '#fff' }\r\n              ])\r\n            },\r\n            label: {\r\n              show: false,\r\n              position: 'top',\r\n              formatter: (val) => `${(val.value / 100).toFixed(2)}℃`\r\n            }\r\n          }\r\n        })\r\n        setTimeout(() => {\r\n          this.$refs.chartRef?.updateOption(chartsData.seriesData)\r\n        })\r\n      }).finally(() => {\r\n        this.loading = false\r\n      })\r\n    },\r\n\r\n  },\r\n\r\n})\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\r\n</style>\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/views/aobo/fieldView/threeJs/components/MarkerDetail.vue b/src/views/aobo/fieldView/threeJs/components/MarkerDetail.vue
--- a/src/views/aobo/fieldView/threeJs/components/MarkerDetail.vue	(revision 2dc6d68a6118a4994fdfcca9ac577843f012d219)
+++ b/src/views/aobo/fieldView/threeJs/components/MarkerDetail.vue	(date 1756351131146)
@@ -1,5 +1,14 @@
 <template>
-  <LineCharts ref="chartRef" v-loading="loading" />
+  <div>
+    <el-radio-group v-model="currentTab" @change="changeTab">
+      <el-radio-button :label="1">截面分析</el-radio-button>
+      <el-radio-button :label="2">曲线分析</el-radio-button>
+    </el-radio-group>
+    <div style="margin-top: 12px;">
+      <HeatMapChart v-if="currentTab === 1" :data="data" />
+      <LineCharts v-else ref="chartRef" v-loading="loading" />
+    </div>
+  </div>
 </template>
 
 <script>
@@ -7,6 +16,8 @@
 import LineCharts from '@/views/aobo/curveView/modules/lineCharts.vue'
 import { liveTrend } from '@/api/lineCharts'
 import * as echarts from 'echarts'
+import { getCoreTemp } from '@/api/onSiteView'
+import HeatMapChart from './HeatMapChart.vue'
 
 const alarmColorMap = {
   0: '#0000FF',
@@ -17,7 +28,7 @@
 
 export default Vue.extend({
   name: 'MarkerDetail',
-  components: { LineCharts },
+  components: { HeatMapChart, LineCharts },
   props: {
     data: {
       type: Object,
@@ -42,14 +53,16 @@
         { color1: '#9a60b4', color2: 'rgba(154, 96, 180, 0)' },
         { color1: '#ea7ccc', color2: 'rgba(234, 124, 204, 0)' },
       ],
+      currentTab: 1,
 
     }
   },
   created() {
-    this.getChartsData()
+    this.changeTab(this.currentTab)
   },
 
   methods: {
+
     /**
      * 根据温度判断报警等级
      * */
@@ -63,11 +76,11 @@
     },
 
     getChartsData() {
+      const { parentData } = this.data
       const data = {
-        segmentIds: [this.data.id],
-        date: this.date,
-        min: this.data.startPosition,
-        max: this.data.endPosition
+        segmentIds: [parentData.id],
+        min: parentData.startPosition,
+        max: parentData.endPosition
       }
       this.loading = true
       // this.$refs.chartRef?.chart?.clear()
@@ -151,6 +164,15 @@
       })
     },
 
+    changeTab(tab) {
+      switch (tab) {
+        case 1:
+          break
+        case 2:
+          this.getChartsData()
+          break
+      }
+    }
   },
 
 })
Index: src/views/aobo/fieldView/index.vue
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><template>\r\n  <div v-loading=\"loading || !threeJsLoaded\" class=\"main-content\">\r\n    <div class=\"top\">\r\n      <div style=\"display: flex; align-items: center\">\r\n        <div style=\"font-weight: bold\">数据刷新间隔时间:</div>\r\n        <el-select v-model=\"timeInterval\" placeholder=\"请选择\" filterable allow-create style=\"width: 100px; margin: 0 10px\" @change=\"timeIntervalChange\">\r\n          <el-option v-for=\"item in timeList\" :key=\"item\" :label=\"item\" :value=\"item\" />\r\n        </el-select>\r\n        <div style=\"font-weight: bold\">秒</div>\r\n        <div style=\"margin-left: 20px\">\r\n          <span style=\"font-weight: bold\">模型精度:</span>\r\n          <el-select v-model=\"precision\" placeholder=\"请选择\" filterable allow-create style=\"width: 100px; margin: 0 10px\" @change=\"precisionChange\">\r\n            <el-option v-for=\"(label, value) in precisionOption\" :key=\"value\" :label=\"label\" :value=\"value\" />\r\n          </el-select>\r\n        </div>\r\n        <!--        <div style=\"margin-left: 20px;\">-->\r\n        <!--          <span>自动巡览：</span>-->\r\n        <!--          <el-switch-->\r\n        <!--            v-model=\"autoView\"-->\r\n        <!--            @change=\"changeAutoView\"-->\r\n        <!--          />-->\r\n\r\n        <!--        </div>-->\r\n      </div>\r\n      <div style=\"display: flex; align-items: center\">\r\n        <el-button style=\"margin-right: 20px\" type=\"primary\" @click=\"toHead\">首部</el-button>\r\n        <el-button style=\"margin-right: 20px\" type=\"danger\" @click=\"toEnd\">尾部</el-button>\r\n        <div style=\"fon-size: 15px; font-weight: bold; margin-right: 20px\">\r\n          {{ nowTime }}\r\n        </div>\r\n        <img style=\"width: 20px; height: 20px; cursor: pointer\" src=\"@/assets/page/quanping.png\" @click=\"handleFullScreen\" />\r\n      </div>\r\n    </div>\r\n    <div\r\n      id=\"content\"\r\n      class=\"content\"\r\n      :class=\"isFullscreen ? 'full_content' : 'normal_content'\"\r\n      style=\"background-size: 100% 100% !important; background-repeat: no-repeat\"\r\n    >\r\n      <div v-if=\"showFullAlarm\" id=\"full-alarm\" class=\"full_alarm\">\r\n        <div />\r\n      </div>\r\n      <ThreeJs ref=\"threeJsRef\" :pipeline-list=\"segmentInfo.cables\" />\r\n      <div class=\"left_top\">\r\n        <div v-if=\"segmentInfo.refreshTime\" class=\"time\" style=\"margin-top: 20px\">\r\n          <img src=\"@/assets/page/<EMAIL>\" />\r\n          <div>{{ `当前数据采集时间：${segmentInfo.refreshTime || '---'}` }}</div>\r\n        </div>\r\n        <div v-if=\"deviceList\" class=\"info_box\">\r\n          <div class=\"base_info\">\r\n            <template>\r\n              <div v-for=\"item in deviceList\" :key=\"item.id\" style=\"margin-bottom: 18px\">\r\n                <div class=\"base_info_item\">\r\n                  <span style=\"color: #c4ddff\">DTS主机信息：</span>\r\n                  <span style=\"color: #ffffff\">\r\n                    {{ item.name || '--' }}\r\n                  </span>\r\n                </div>\r\n                <div class=\"base_info_item\">\r\n                  <span style=\"color: #c4ddff\">刷新时间：</span>\r\n                  <span style=\"color: #ffffff\">\r\n                    {{ item.refreshInterval || item.refreshInterval === 0 ? `${item.refreshInterval}s` : '--' }}\r\n                  </span>\r\n                </div>\r\n                <div class=\"base_info_item\" style=\"margin-bottom: 3px\">\r\n                  <span style=\"color: #c4ddff\">光缆信息：</span>\r\n                </div>\r\n                <template v-if=\"item.fiberList\">\r\n                  <div v-for=\"item1 in item.fiberList\" :key=\"item1.id\" style=\"margin-bottom: 10px; margin-left: 5px\">\r\n                    <div class=\"base_info_item\">\r\n                      <span style=\"color: #ffffff\">{{ item1.name || '--' }}</span>\r\n                    </div>\r\n                    <div class=\"base_info_item\">\r\n                      <span style=\"color: #c4ddff\">长度：</span>\r\n                      <span style=\"color: #ffffff\">\r\n                        {{ item1.fiberLength || item1.fiberLength === 0 ? `${item1.fiberLength / 100}m` : '--' }}\r\n                      </span>\r\n                    </div>\r\n                    <div class=\"base_info_item\">\r\n                      <span style=\"color: #c4ddff\">纤芯类型：</span>\r\n                      <span style=\"color: #ffffff\">\r\n                        {{ item1.fibreCoreSize || '--' }}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                </template>\r\n              </div>\r\n            </template>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <img v-if=\"isFullscreen\" src=\"@/assets/page/tuichu.png\" title=\"退出全屏\" class=\"out_fullscreen\" @click=\"handleFullScreen\" />\r\n      <AlarmBattery\r\n        ref=\"alarmBattery\"\r\n        :is-fullscreen=\"isFullscreen\"\r\n        :alarm-list=\"segmentInfo.alarms\"\r\n        @detail=\"toAlarmLine\"\r\n      />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport dayjs from 'dayjs'\r\nimport { deviceDropdown } from '@/api/lineCharts'\r\nimport Cookies from 'js-cookie'\r\nimport { playAudio } from '@/utils/palyAudio'\r\nimport { useDebounceFn } from '@/utils'\r\nimport AlarmBattery from '@/views/aobo/fieldView/components/alarmList/index'\r\nimport store from '@/store'\r\nimport { camera, environment, orbitControls } from '@/views/aobo/fieldView/threeJs/service/core'\r\nimport gsap from 'gsap'\r\nimport * as THREE from 'three'\r\nimport { segmentList } from '@/api/onSiteView'\r\nimport ThreeJs from './threeJs/index.vue'\r\n\r\n// 报警持续闪烁时间（s）\r\nconst ALARM_TIME = 10\r\nexport default {\r\n  name: 'FieldView',\r\n  components: {\r\n    ThreeJs,\r\n    AlarmBattery,\r\n  },\r\n  data() {\r\n    return {\r\n      segmentInfo: { alarms: [] },\r\n      autoView: false,\r\n      btnAuthorityList: [], // 按钮权限\r\n      nowTime: null,\r\n      detailData: {},\r\n      precisionOption: {\r\n        0: '流畅',\r\n        1: '高清',\r\n        2: '极致',\r\n      },\r\n      precision: localStorage.getItem('precision') || '1',\r\n      timeList: [30, 60, 90, 120], // 间隔时间下拉框\r\n      timeInterval: Number(localStorage.getItem('timeInterval')) || 60,\r\n      intervalNow: null, // 当前数据采集时间\r\n      isFullscreen: false, // 是否全屏\r\n      showFullAlarm: false, // 是否展示全屏告警\r\n      socket: null, // WebSocket\r\n      socketCode: 1, // 状态码\r\n      closeAudio: null,\r\n      loading: false,\r\n      timer: null, // 显示全屏报警定时器\r\n      timer1: null,\r\n      deviceList: [],\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters(['threeJsLoaded', 'btnAuthority', 'threeJsProgress']),\r\n  },\r\n  created() {\r\n    this.getSegmentInfo()\r\n    const cur = this.btnAuthority.find((item) => item.code === this.$route.meta.code)\r\n    if (cur) this.btnAuthorityList = cur.functionPermissionCode\r\n    this.getDeviceList()\r\n  },\r\n  mounted() {\r\n    this.websocketConnect()\r\n    this.nowTime = dayjs().format('YYYY年MM月DD日 HH:mm:ss')\r\n    this.intervalNow = setInterval(() => {\r\n      this.nowTime = dayjs().format('YYYY年MM月DD日 HH:mm:ss')\r\n    }, 1000)\r\n  },\r\n  beforeDestroy() {\r\n    store.dispatch('threeJs/setLoaded', false)\r\n    this.socket.close()\r\n    this.socket = null\r\n    this.closeAudio?.()\r\n    clearInterval(this.intervalNow)\r\n    clearTimeout(this.timer)\r\n    clearTimeout(this.timer1)\r\n  },\r\n  methods: {\r\n    getSegmentInfo(showLoading = true) {\r\n      if (showLoading) {\r\n        this.loading = true\r\n      }\r\n      segmentList()\r\n        .then((res) => {\r\n          this.segmentInfo = res?.data || { alarms: [] }\r\n          this.timer1 = setTimeout(() => {\r\n            this.getSegmentInfo(false)\r\n          }, this.timeInterval * 1000)\r\n        })\r\n        .finally(() => {\r\n          this.loading = false\r\n        })\r\n    },\r\n    /**\r\n     * 获取设备列表\r\n     * */\r\n    getDeviceList() {\r\n      deviceDropdown().then((res) => {\r\n        this.deviceList = res?.data || []\r\n      })\r\n    },\r\n\r\n    /**\r\n     * 切换模型精度\r\n     * */\r\n    precisionChange() {\r\n      localStorage.setItem('precision', this.precision)\r\n      window.location.reload()\r\n    },\r\n    /**\r\n     * 自动巡览\r\n     * */\r\n    changeAutoView(value) {\r\n      if (!this.threeJsLoaded) {\r\n        this.$message.warning('模型加载中')\r\n        return\r\n      }\r\n      console.log('自动巡览', value)\r\n      this.toHead().then(() => {\r\n        const progress = { value: 0 }\r\n        gsap.to(progress, {\r\n          value: 0.99,\r\n          duration: 60,\r\n          ease: 'none',\r\n          onUpdate: () => {\r\n            const targetT = Math.min(progress.value + 0.01, 1)\r\n            orbitControls.target.copy(environment.centerCurve.getPoint(targetT))\r\n            orbitControls.update()\r\n            const point = environment.centerCurve.getPoint(progress.value)\r\n            console.log(point)\r\n            camera.position.set(point.x, point.y, point.z)\r\n          },\r\n        })\r\n      })\r\n    },\r\n    websocketConnect() {\r\n      // 石伟\r\n      this.socket = new WebSocket('/ws')\r\n\r\n      // 测试服\r\n      // this.socket = new WebSocket('wss://aobo-dts-test.vankeytech.com:9902/ws')\r\n      // 线上\r\n      // this.socket = new WebSocket('ws://10.108.183.106/ws')\r\n      // 监听socket连接\r\n      this.socket.onopen = () => {\r\n        const data = {\r\n          code: this.socketCode,\r\n          token: Cookies.get('governance_token'),\r\n        }\r\n        this.socket.send(JSON.stringify(data))\r\n      }\r\n      // 监听socket错误信息\r\n      this.socket.onerror = (err) => {\r\n        console.log(err, 'err')\r\n      }\r\n      // 消息防抖处理\r\n      const debouncedFn = useDebounceFn((msg) => {\r\n        const res = JSON.parse(msg.data)\r\n        if (res.code === 200 && this.socketCode === 1) {\r\n          this.socketCode++\r\n          const data = {\r\n            code: this.socketCode,\r\n            msgId: res.message,\r\n          }\r\n          this.socket.send(JSON.stringify(data))\r\n        } else if (res.code === 200 && res.message) {\r\n          const { data } = res\r\n          this.showFullAlarm = true\r\n          if (data.isEnableAudio) {\r\n            this.closeAudio?.()\r\n            this.closeAudio = playAudio(data.audioUrl, data.cycleCount)\r\n          }\r\n\r\n          clearTimeout(this.timer1)\r\n          this.getSegmentInfo(false)\r\n\r\n          clearTimeout(this.timer)\r\n          this.timer = setTimeout(() => {\r\n            this.showFullAlarm = false\r\n          }, ALARM_TIME * 1000)\r\n        }\r\n      }, 1000)\r\n      // 监听socket消息\r\n      this.socket.onmessage = debouncedFn\r\n      this.socket.onclose = () => {\r\n        if (this.$route.meta.code !== 'XCST') return\r\n        this.socketCode = 1\r\n        this.socket = null\r\n        this.websocketConnect()\r\n      }\r\n    },\r\n\r\n    // 选择数据刷新间隔时间\r\n    timeIntervalChange() {\r\n      localStorage.setItem('timeInterval', this.timeInterval)\r\n      clearTimeout(this.timer1)\r\n      this.getSegmentInfo(false)\r\n    },\r\n    handleFullScreen() {\r\n      this.isFullscreen = !this.isFullscreen\r\n      const myEvent = new Event('resize')\r\n      window.dispatchEvent(myEvent)\r\n    },\r\n\r\n    /**\r\n     * 跳转到告警监控段\r\n     * */\r\n    toAlarmLine(row) {\r\n      this.$refs.threeJsRef.goToAlarmMarker(row.id)\r\n    },\r\n    toHead() {\r\n      if (!this.threeJsLoaded) {\r\n        this.$message.warning('模型加载中')\r\n        return\r\n      }\r\n\r\n      const position = new THREE.Vector3(-539, -32, -9)\r\n      orbitControls.target.copy(position)\r\n      orbitControls.update()\r\n      return new Promise((resolve, reject) => {\r\n        gsap.to(camera.position, {\r\n          x: -614,\r\n          y: -3,\r\n          z: -38,\r\n          duration: 1,\r\n          ease: 'power2.inOut',\r\n          onComplete: () => {\r\n            resolve()\r\n          },\r\n        })\r\n      })\r\n    },\r\n    toEnd() {\r\n      if (!this.threeJsLoaded) {\r\n        this.$message.warning('模型加载中')\r\n        return\r\n      }\r\n\r\n      const position = new THREE.Vector3(539, -8, -86.8)\r\n      orbitControls.target.copy(position)\r\n      orbitControls.update()\r\n      gsap.to(camera.position, {\r\n        x: 573,\r\n        y: 28,\r\n        z: -191,\r\n        duration: 1,\r\n        ease: 'power2.inOut',\r\n      })\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@keyframes blink {\r\n  0% {\r\n    opacity: 0;\r\n  }\r\n  50% {\r\n    opacity: 0.5;\r\n  }\r\n  100% {\r\n    opacity: 0;\r\n  }\r\n}\r\n.main-content {\r\n  //height: calc(100%);\r\n  padding-top: 30px;\r\n  box-sizing: border-box;\r\n  font-family: PingFang SC RE;\r\n  .top {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    margin-bottom: 20px;\r\n  }\r\n  .normal_content {\r\n    width: 100%;\r\n    height: calc(100vh - 230px);\r\n    position: relative;\r\n  }\r\n  .full_content {\r\n    width: 1920px;\r\n    height: 100vh;\r\n    position: fixed;\r\n    left: 0;\r\n    top: 0;\r\n  }\r\n  .content {\r\n    background: url(~@/assets/page/model_bg.webp) no-repeat;\r\n    background-size: 100% 100%;\r\n    z-index: 999;\r\n    #full-alarm::before,\r\n    #full-alarm::after {\r\n      content: '';\r\n      position: absolute;\r\n      width: 120px;\r\n      height: 100%;\r\n    }\r\n    #full-alarm > div::before,\r\n    #full-alarm > div::after {\r\n      content: '';\r\n      position: absolute;\r\n      width: 100%;\r\n      height: 120px;\r\n    }\r\n    #full-alarm::before {\r\n      background: linear-gradient(to right, red, transparent);\r\n      top: 0;\r\n      left: 0;\r\n      transform: rotate(0deg);\r\n    }\r\n    #full-alarm::after {\r\n      background: linear-gradient(to left, red, transparent);\r\n      top: 0%;\r\n      left: 100%;\r\n      transform: rotate(0deg) translate(calc(-1 * 120px), 0px);\r\n    }\r\n    #full-alarm > div::before {\r\n      background: linear-gradient(to top, red, transparent);\r\n      top: 0;\r\n      left: 0;\r\n      transform: rotate(180deg);\r\n    }\r\n    #full-alarm > div::after {\r\n      background: linear-gradient(to top, red, transparent);\r\n      top: 100%;\r\n      left: 0;\r\n      transform: rotate(0deg) translate(0px, calc(-1 * 120px));\r\n    }\r\n    #full-alarm {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      width: 100%;\r\n      height: 100%;\r\n      box-sizing: border-box;\r\n      opacity: 1;\r\n      transition: opacity 0.5s;\r\n      pointer-events: none;\r\n      z-index: 999;\r\n      animation: blink 1s infinite;\r\n    }\r\n    .left_top {\r\n      position: absolute;\r\n      left: 20px;\r\n      top: 10px;\r\n      z-index: 999;\r\n      .room_title {\r\n        display: flex;\r\n        align-items: center;\r\n        // height: 40px;\r\n        margin-top: -10px;\r\n        .img1 {\r\n          width: 50px;\r\n          height: 60px;\r\n          margin-top: 15px;\r\n        }\r\n        .img2 {\r\n          width: 25px;\r\n          height: 20px;\r\n          margin: 0 5px;\r\n        }\r\n        div {\r\n          // font-size: 22px !important;\r\n          // padding-top: 10px;\r\n          padding-right: 5px;\r\n          font-family: PingFang SC;\r\n          font-weight: bold;\r\n          background: linear-gradient(0deg, #0279fa 25%, #e8eeff 75%);\r\n          -webkit-background-clip: text;\r\n          -webkit-text-fill-color: transparent;\r\n        }\r\n      }\r\n      .time {\r\n        display: flex;\r\n        margin-bottom: 20px;\r\n        align-items: center;\r\n        img {\r\n          width: 15px;\r\n          height: 15px;\r\n          margin-right: 10px;\r\n        }\r\n        div {\r\n          color: #91a0b4;\r\n          font-size: 13px;\r\n          font-weight: bold;\r\n          display: flex;\r\n        }\r\n      }\r\n      .info_box {\r\n        width: 250px;\r\n        background: url('../../../assets/page/kuang1.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        padding: 20px 23px !important;\r\n        .base_info {\r\n          height: 300px !important;\r\n          overflow: auto;\r\n          .base_info_item {\r\n            line-height: 27px;\r\n            font-size: 13px;\r\n            // font-weight: bold;\r\n            // font-family: PingFang SC RE;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .left_bottom {\r\n      position: absolute;\r\n      left: 20px;\r\n      bottom: 30px;\r\n      width: 220px;\r\n      height: 65px;\r\n      z-index: 999;\r\n      background: url('../../../assets/page/kuang2.png') no-repeat;\r\n      background-size: 100% 100%;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      font-size: 13px;\r\n      .left_bottom_item {\r\n        width: 25%;\r\n        height: 90%;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        position: absolute;\r\n        > :first-child {\r\n          font-size: 16px;\r\n        }\r\n        div {\r\n          // font-weight: bold;\r\n        }\r\n      }\r\n      .low {\r\n        top: -12px;\r\n        left: 0;\r\n        div:nth-child(1) {\r\n          background: linear-gradient(0, #5bd943 0%, #ffffff 100%);\r\n          -webkit-background-clip: text;\r\n          -webkit-text-fill-color: transparent;\r\n          font-weight: bold;\r\n        }\r\n      }\r\n      .center {\r\n        top: -12px;\r\n        left: 40%;\r\n        div:nth-child(1) {\r\n          background: linear-gradient(0, #fab237 0%, #ffffff 100%);\r\n          -webkit-background-clip: text;\r\n          -webkit-text-fill-color: transparent;\r\n          font-weight: bold;\r\n        }\r\n      }\r\n      .hign {\r\n        top: -12px;\r\n        right: 0;\r\n        div:nth-child(1) {\r\n          background: linear-gradient(0, #e53730 0%, #ffffff 100%);\r\n          -webkit-background-clip: text;\r\n          -webkit-text-fill-color: transparent;\r\n          font-weight: bold;\r\n        }\r\n      }\r\n    }\r\n    .out_fullscreen {\r\n      position: absolute;\r\n      right: 50px;\r\n      top: 20px;\r\n      width: 50px;\r\n      height: 50px;\r\n      cursor: pointer;\r\n      z-index: 999 !important;\r\n    }\r\n  }\r\n  .dialog_footer {\r\n    display: flex;\r\n    justify-content: center;\r\n    // margin-top: 20px;\r\n  }\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\n.base_info {\r\n  &::-webkit-scrollbar {\r\n    width: 5px !important;\r\n    cursor: pointer;\r\n  }\r\n\r\n  &::-webkit-scrollbar-track {\r\n    height: 10px !important;\r\n    width: 5px !important;\r\n    background: rgba(81, 120, 192, 0.3);\r\n    border-radius: 2px;\r\n    cursor: pointer;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    height: 10px !important;\r\n    width: 5px !important;\r\n    background: rgba(30, 107, 248, 0.3);\r\n    border-radius: 10px;\r\n    cursor: pointer;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb:hover {\r\n    background: rgba(19, 77, 184, 0.3);\r\n    cursor: pointer;\r\n  }\r\n\r\n  &::-webkit-scrollbar-corner {\r\n    background: rgba(27, 95, 225, 0.3);\r\n  }\r\n}\r\n.pagination {\r\n  // width: 250px !important;\r\n  margin-top: 20px !important;\r\n  .el-pagination__total {\r\n    min-width: 25px !important;\r\n    height: 25px !important;\r\n    line-height: 25px !important;\r\n    color: rgb(196, 196, 196) !important;\r\n    font-size: 12px !important;\r\n  }\r\n  button {\r\n    background: transparent !important;\r\n    color: rgb(196, 196, 196) !important;\r\n    border: 1px solid #686a6b !important;\r\n    min-width: 23px !important;\r\n    height: 25px !important;\r\n  }\r\n  .number {\r\n    background: transparent !important;\r\n    color: rgb(196, 196, 196) !important;\r\n    border: 1px solid #686a6b !important;\r\n    min-width: 23px !important;\r\n    height: 25px !important;\r\n    line-height: 25px !important;\r\n    margin: 0 3px !important;\r\n    font-size: 12px !important;\r\n  }\r\n  .active {\r\n    background: #006be4 !important;\r\n    border: none !important;\r\n    min-width: 23px !important;\r\n    height: 25px !important;\r\n    line-height: 25px !important;\r\n  }\r\n  .more {\r\n    background: transparent !important;\r\n    border: 1px solid #686a6b !important;\r\n    min-width: 23px !important;\r\n    height: 25px !important;\r\n    line-height: 25px !important;\r\n  }\r\n}\r\n\r\n.no_data {\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: calc(100vh - 200px);\r\n  img {\r\n    width: 250px;\r\n    height: 200px;\r\n  }\r\n  div {\r\n    font-size: 20px;\r\n    font-weight: bold;\r\n    color: #7d7d7d;\r\n  }\r\n}\r\n.list_container {\r\n  height: calc(100% - 130px);\r\n  overflow-y: auto;\r\n  overflow-x: hidden;\r\n  scrollbar-width: none;\r\n}\r\n</style>\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/views/aobo/fieldView/index.vue b/src/views/aobo/fieldView/index.vue
--- a/src/views/aobo/fieldView/index.vue	(revision 2dc6d68a6118a4994fdfcca9ac577843f012d219)
+++ b/src/views/aobo/fieldView/index.vue	(date 1756279052256)
@@ -238,10 +238,6 @@
       // 石伟
       this.socket = new WebSocket('/ws')
 
-      // 测试服
-      // this.socket = new WebSocket('wss://aobo-dts-test.vankeytech.com:9902/ws')
-      // 线上
-      // this.socket = new WebSocket('ws://10.108.183.106/ws')
       // 监听socket连接
       this.socket.onopen = () => {
         const data = {
Index: vue.config.js
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>const path = require('path')\r\nconst CopyWebpackPlugin = require('copy-webpack-plugin')\r\nconst defaultSettings = require('./src/settings')\r\n\r\nfunction resolve(dir) {\r\n  return path.join(__dirname, dir)\r\n}\r\n\r\nconst name = defaultSettings.title || 'vue Admin Template' // page title\r\n\r\n// If your port is set to 80,\r\n// use administrator privileges to execute the command line.\r\n// For example, Mac: sudo npm run\r\n// You can change the port by the following methods:\r\n// port = 9528 npm run dev OR npm run dev --port = 9528\r\nconst port = process.env.port || process.env.npm_config_port || 8999 // dev port\r\n\r\n// All configuration item explanations can be find in https://cli.vuejs.org/config/\r\nmodule.exports = {\r\n  /**\r\n   * You will need to set publicPath if you plan to deploy your site under a sub path,\r\n   * for example GitHub Pages. If you plan to deploy your site to https://foo.github.io/bar/,\r\n   * then publicPath should be set to \"/bar/\".\r\n   * In most cases please use '/' !!!\r\n   * Detail: https://cli.vuejs.org/config/#publicpath\r\n   */\r\n  publicPath: '/',\r\n  outputDir: 'dist',\r\n  assetsDir: 'static',\r\n  lintOnSave: process.env.NODE_ENV === 'development',\r\n  productionSourceMap: false,\r\n  transpileDependencies: ['screenfull', '@geoman-io/leaflet-geoman-free', 'three-mesh-bvh'],\r\n  devServer: {\r\n    port,\r\n    open: true,\r\n    hot: true,\r\n    host: '0.0.0.0',\r\n    overlay: {\r\n      warnings: false,\r\n      errors: true,\r\n    },\r\n    proxy: {\r\n      '/api': {\r\n        // target: 'http://************:8071', // 石伟本地\r\n        // target: 'http://************:8071', // 石伟本地\r\n        target: 'https://whzl.vankeytech.com:18956', // 测试环境\r\n        changeOrigin: true,\r\n      },\r\n      '/ws': {\r\n        target: 'ws://************:21021', // 石伟本地\r\n        // target: 'https://whzl.vankeytech.com:18956', // 测试环境\r\n        ws: true,\r\n        changeOrigin: true,\r\n      },\r\n    },\r\n    // before: require('./mock/mock-server')\r\n  },\r\n  runtimeCompiler: true,\r\n  configureWebpack: {\r\n    // provide the app's title in webpack's name field, so that\r\n    // it can be accessed in index.html to inject the correct title.\r\n    name,\r\n    devtool: 'source-map',\r\n    resolve: {\r\n      alias: {\r\n        '@': resolve('src'),\r\n      },\r\n    },\r\n    plugins: [],\r\n    externals: {\r\n      AMap: 'AMap',\r\n    },\r\n  },\r\n  chainWebpack(config) {\r\n    // it can improve the speed of the first screen, it is recommended to turn on preload\r\n    config.plugin('preload').tap(() => [\r\n      {\r\n        rel: 'preload',\r\n        // to ignore runtime.js\r\n        // https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/cli-service/lib/config/app.js#L171\r\n        fileBlacklist: [/\\.map$/, /hot-update\\.js$/, /runtime\\..*\\.js$/],\r\n        include: 'initial',\r\n      },\r\n    ])\r\n\r\n    // when there are many pages, it will cause too many meaningless requests\r\n    config.plugins.delete('prefetch')\r\n\r\n    // set svg-sprite-loader\r\n    config.module.rule('svg').exclude.add(resolve('src/icons')).end()\r\n    config.module\r\n      .rule('icons')\r\n      .test(/\\.svg$/)\r\n      .include.add(resolve('src/icons'))\r\n      .end()\r\n      .use('svg-sprite-loader')\r\n      .loader('svg-sprite-loader')\r\n      .options({\r\n        symbolId: 'icon-[name]',\r\n      })\r\n      .end()\r\n\r\n    config.when(process.env.NODE_ENV !== 'development', (config1) => {\r\n      config1\r\n        .plugin('ScriptExtHtmlWebpackPlugin')\r\n        .after('html')\r\n        .use('script-ext-html-webpack-plugin', [\r\n          {\r\n            // `runtime` must same as runtimeChunk name. default is `runtime`\r\n            inline: /runtime\\..*\\.js$/,\r\n          },\r\n        ])\r\n        .end()\r\n      config1.optimization\r\n        .splitChunks({\r\n          chunks: 'all',\r\n          cacheGroups: {\r\n            libs: {\r\n              name: 'chunk-libs',\r\n              test: /[\\\\/]node_modules[\\\\/]/,\r\n              priority: 10,\r\n              chunks: 'initial', // only package third parties that are initially dependent\r\n            },\r\n            elementUI: {\r\n              name: 'chunk-elementUI', // split elementUI into a single package\r\n              // the weight needs to be larger than libs\r\n              // and app or it will be packaged into libs or app\r\n              priority: 20,\r\n              test: /[\\\\/]node_modules[\\\\/]_?element-ui(.*)/, // in order to adapt to cnpm\r\n            },\r\n            commons: {\r\n              name: 'chunk-commons',\r\n              test: resolve('src/components'), // can customize your rules\r\n              minChunks: 3, //  minimum common number\r\n              priority: 5,\r\n              reuseExistingChunk: true,\r\n            },\r\n          },\r\n        })\r\n        .minimizer('terser')\r\n        .tap((args) => {\r\n          args[0].terserOptions.compress.drop_console = true // 移除console\r\n          args[0].terserOptions.compress.drop_debugger = true // 移除debugger\r\n          return args\r\n        })\r\n      // https:// webpack.js.org/configuration/optimization/#optimizationruntimechunk\r\n      config1.optimization.runtimeChunk('single')\r\n    })\r\n  },\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/vue.config.js b/vue.config.js
--- a/vue.config.js	(revision 2dc6d68a6118a4994fdfcca9ac577843f012d219)
+++ b/vue.config.js	(date 1756350616471)
@@ -47,8 +47,8 @@
         changeOrigin: true,
       },
       '/ws': {
-        target: 'ws://************:21021', // 石伟本地
-        // target: 'https://whzl.vankeytech.com:18956', // 测试环境
+        // target: 'ws://************:21021', // 石伟本地
+        target: 'https://whzl.vankeytech.com:18956', // 测试环境
         ws: true,
         changeOrigin: true,
       },
Index: src/api/onSiteView.js
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import request from '@/utils/request'\r\n\r\n// 分段列表\r\nexport function segmentList() {\r\n  return request({\r\n    url: '/api/v1/site',\r\n    method: 'get'\r\n  })\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/api/onSiteView.js b/src/api/onSiteView.js
--- a/src/api/onSiteView.js	(revision 2dc6d68a6118a4994fdfcca9ac577843f012d219)
+++ b/src/api/onSiteView.js	(date 1756344505353)
@@ -7,3 +7,12 @@
     method: 'get'
   })
 }
+
+// 获取缆芯温度
+export function getCoreTemp(data) {
+  return request({
+    url: '/api/v1/cable/coreTemp',
+    method: 'get',
+    params: data
+  })
+}
Index: src/views/aobo/fieldView/threeJs/components/HeatMapChart.vue
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/views/aobo/fieldView/threeJs/components/HeatMapChart.vue b/src/views/aobo/fieldView/threeJs/components/HeatMapChart.vue
new file mode 100644
--- /dev/null	(date 1756362725011)
+++ b/src/views/aobo/fieldView/threeJs/components/HeatMapChart.vue	(date 1756362725011)
@@ -0,0 +1,159 @@
+<script>
+import Vue from 'vue'
+import * as echarts from 'echarts'
+import { getCoreTemp } from '@/api/onSiteView'
+
+function hexToRgb(hex) {
+  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
+  return result ? {
+    r: parseInt(result[1], 16),
+    g: parseInt(result[2], 16),
+    b: parseInt(result[3], 16)
+  } : null
+}
+
+// 颜色插值函数
+function interpolateColor(color1, color2, ratio) {
+  const c1 = hexToRgb(color1)
+  const c2 = hexToRgb(color2)
+
+  const r = Math.round(c1.r + (c2.r - c1.r) * ratio)
+  const g = Math.round(c1.g + (c2.g - c1.g) * ratio)
+  const b = Math.round(c1.b + (c2.b - c1.b) * ratio)
+
+  return `rgb(${r}, ${g}, ${b})`
+}
+
+// 温度到颜色的映射函数
+function getColorByTemperature(temp) {
+  const colors = [
+    { temp: 10, color: '#000080' }, // 深蓝色
+    { temp: 20, color: '#0000FF' }, // 蓝色
+    { temp: 30, color: '#00BFFF' }, // 深天蓝色
+    { temp: 40, color: '#00FFFF' }, // 青色
+    { temp: 50, color: '#7FFF00' }, // 黄绿色
+    { temp: 60, color: '#FFFF00' }, // 黄色
+    { temp: 70, color: '#FF7F00' }, // 橙色
+    { temp: 80, color: '#FF0000' }, // 红色
+    { temp: 90, color: '#8B0000' }, // 深红色
+  ]
+
+  for (let i = 0; i < colors.length - 1; i++) {
+    if (temp >= colors[i].temp && temp <= colors[i + 1].temp) {
+      // 线性插值
+      const ratio = (temp - colors[i].temp) / (colors[i + 1].temp - colors[i].temp)
+      return interpolateColor(colors[i].color, colors[i + 1].color, ratio)
+    }
+  }
+
+  return temp <= colors[0].temp ? colors[0].color : colors[colors.length - 1].color
+}
+
+export default Vue.extend({
+  name: 'HeatMapChart',
+  props: {
+    data: {
+      type: Object,
+      default: () => ({})
+    },
+  },
+  data() {
+    return {
+      loading: true,
+      tempList: [],
+    }
+  },
+  created() {
+    this.getCoreTemp()
+  },
+  mounted() {
+  },
+  beforeDestroy() {
+    // 清理事件监听
+    window.removeEventListener('resize', () => {})
+  },
+  methods: {
+    /**
+     * 获取缆芯温度
+     * */
+    getCoreTemp() {
+      this.loading = true
+      getCoreTemp({ current: this.data.parentData.current || 300, surfaceTemp: Math.floor((this.data.temperature / 100)) }).then((res) => {
+        this.tempList = Object.values(res.data)
+        this.initChart()
+      }).finally(() => {
+        this.loading = false
+      })
+    },
+
+    initChart() {
+      const chart = echarts.init(this.$refs.chartRef)
+
+      const option = {
+        tooltip: {
+          trigger: 'item',
+          formatter(params) {
+            const temperature = params.data
+            return `温度: ${Math.round(temperature * 100) / 100}°C<br/>电流: 1960A`
+          }
+        },
+        xAxis: {
+          type: 'value',
+          show: false,
+          min: 0,
+          max: 300
+        },
+        yAxis: {
+          type: 'value',
+          show: false,
+          min: 0,
+          max: 300
+        },
+        series: [{
+          type: 'custom',
+          coordinateSystem: 'cartesian2d',
+          data: this.tempList,
+          renderItem: (params, api) => {
+            const w = api.getWidth()
+            const h = api.getHeight()
+            // 每一份圆环长度
+            const unitLength = Math.min(w, h) / 2 / this.tempList.length
+            const temp = api.value(0)
+
+            const fillColor = getColorByTemperature(temp)
+
+            return {
+              type: 'sector',
+              shape: {
+                cx: w / 2,
+                cy: h / 2,
+                r0: unitLength * params.dataIndex,
+                r: unitLength * (params.dataIndex + 1),
+                startAngle: 0,
+                endAngle: Math.PI * 2,
+              },
+              style: {
+                fill: fillColor,
+                stroke: 'transparent',
+                lineWidth: 0,
+              }
+            }
+          }
+        }],
+      }
+
+      chart.setOption(option)
+    }
+  }
+})
+</script>
+
+<template>
+  <div v-loading="loading">
+    <div ref="chartRef" style="width: 400px; height: 400px;"></div>
+
+  </div>
+</template>
+
+<style scoped lang="scss">
+</style>
Index: src/styles/index.scss
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>@import './variables.scss';\r\n@import './mixin.scss';\r\n@import './transition.scss';\r\n@import './element-ui.scss';\r\n@import './sidebar.scss';\r\n\r\nbody {\r\n  height: 100%;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  -webkit-font-smoothing: antialiased;\r\n  text-rendering: optimizeLegibility;\r\n  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;\r\n}\r\n\r\nlabel {\r\n  font-weight: 700;\r\n}\r\n\r\nhtml {\r\n  height: 100%;\r\n  box-sizing: border-box;\r\n}\r\n\r\n#app {\r\n  height: 100%;\r\n  overflow: auto;\r\n}\r\n\r\n*,\r\n*:before,\r\n*:after {\r\n  box-sizing: inherit;\r\n}\r\n\r\na:focus,\r\na:active {\r\n  outline: none;\r\n}\r\n\r\na,\r\na:focus,\r\na:hover {\r\n  cursor: pointer;\r\n  color: inherit;\r\n  text-decoration: none;\r\n}\r\n\r\ndiv:focus {\r\n  outline: none;\r\n}\r\n\r\n.clearfix {\r\n  &:after {\r\n    visibility: hidden;\r\n    display: block;\r\n    font-size: 0;\r\n    content: ' ';\r\n    clear: both;\r\n    height: 0;\r\n  }\r\n}\r\n\r\n// main-container global css\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n@font-face {\r\n  font-family: 'PingFang SC RE';\r\n  src: url('../font/PingFang\\ Regular.ttf');\r\n}\r\n\r\n@font-face {\r\n  font-family: 'Source Han Sans CN RE';\r\n  src: url('../font/SourceHanSansCN-Medium.otf');\r\n}\r\n\r\n@font-face {\r\n  font-family: 'Source Han Sans CN Regular';\r\n  src: url('../font/SourceHanSansCN-Regular.otf');\r\n}\r\n\r\n@font-face {\r\n  font-family: 'Title';\r\n  src: url('../font/title_font.ttf');\r\n}\r\n\r\n// ----自定义统一样式 -start\r\n.item-label {\r\n  color: rgba(0, 0, 0, 0.85);\r\n}\r\n\r\n.item-info {\r\n  color: rgba(0, 0, 0, 0.65);\r\n}\r\n\r\n.card-large {\r\n  padding: 30px 40px;\r\n  background: #fff;\r\n  border-radius: 5px;\r\n}\r\n\r\n.page-top {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 80px;\r\n  line-height: 80px;\r\n  padding: 0 20px;\r\n  box-shadow: 0px 0px 16px 4px rgba(15, 42, 104, 0.08);\r\n  border-radius: 10px;\r\n  margin-bottom: 20px;\r\n  background: #fff;\r\n\r\n  .page-top-title {\r\n    font-size: 18px;\r\n    font-family: Source Han Sans CN;\r\n    font-weight: bold;\r\n    color: #202020;\r\n  }\r\n\r\n  .filters {\r\n    > * {\r\n      margin-right: 20px;\r\n    }\r\n  }\r\n\r\n  .buttons {\r\n    margin-left: auto;\r\n  }\r\n}\r\n\r\n.page-content {\r\n  background: #ffffff;\r\n  box-shadow: 0px 0px 16px 4px rgba(15, 42, 104, 0.08);\r\n  border-radius: 6px;\r\n}\r\n\r\n.box-title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  margin-bottom: 25px;\r\n  color: #202020;\r\n\r\n  &::before {\r\n    content: '';\r\n    display: inline-block;\r\n    margin-right: 10px;\r\n    width: 5px;\r\n    height: 16px;\r\n    background: linear-gradient(180deg, #50a7ff 0%, #ffffff 100%);\r\n  }\r\n}\r\n\r\n.icon-in-input {\r\n  font-weight: bold;\r\n  cursor: pointer;\r\n  // color: #0061CE\r\n}\r\n\r\n.infinite-list {\r\n  height: calc(100vh - 300px);\r\n  overflow-y: scroll;\r\n}\r\n\r\n.noMore {\r\n  height: 60px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  color: #b3b3b3;\r\n}\r\n\r\n.table-overflow-content {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  cursor: pointer;\r\n}\r\n\r\n// 表格超过三行省略\r\n.omit {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 3;\r\n  -webkit-box-orient: vertical;\r\n}\r\n\r\n// ----自定义统一样式 -end\r\n\r\n// ----表单长短 -start\r\n.medium-form-item {\r\n  width: 200px !important;\r\n}\r\n\r\n.large-form-item {\r\n  width: 300px !important;\r\n}\r\n\r\n// ----表单长短 -end\r\n\r\n// ----ui规范 -start\r\n.heading1 {\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n}\r\n\r\n.heading2 {\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n  color: #212121;\r\n}\r\n\r\n.heading3 {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n\r\n.tips-words {\r\n  font-size: 13px;\r\n  color: #999;\r\n}\r\n\r\n// ----ui规范 -end\r\n\r\n// ----element-ui样式修改 -start\r\n// 表格\r\n.content-table {\r\n  .table-header {\r\n    th {\r\n      background: rgba(219, 222, 233, 0.25) !important;\r\n      font-size: 15px !important;\r\n      font-family: PingFang SC RE;\r\n      font-weight: bold;\r\n      color: #202020;\r\n      height: 30px !important;\r\n      line-height: 30px !important;\r\n      padding: 10px 0 !important;\r\n      position: relative;\r\n\r\n      // &:not(:last-child)::after {\r\n      //   content: '';\r\n      //   position: absolute;\r\n      //   right: 0;\r\n      //   top: 22px;\r\n      //   width: 1px;\r\n      //   height: 20px;\r\n      //   background: #DDE1EE;\r\n      // }\r\n    }\r\n  }\r\n\r\n  td {\r\n    height: 60px;\r\n    line-height: 60px;\r\n    font-size: 14px;\r\n    font-family: Source Han Sans CN Regular;\r\n    // font-weight: bold;\r\n    color: #555555;\r\n  }\r\n}\r\n\r\n// 滚动条样式\r\n* {\r\n  &::-webkit-scrollbar {\r\n    height: 10px !important;\r\n    width: 10px !important;\r\n    cursor: pointer;\r\n  }\r\n\r\n  &::-webkit-scrollbar-track {\r\n    height: 10px !important;\r\n    width: 10px !important;\r\n    background: rgb(239, 239, 239);\r\n    border-radius: 2px;\r\n    cursor: pointer;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    height: 10px !important;\r\n    width: 10px !important;\r\n    background: #bfbfbf;\r\n    border-radius: 10px;\r\n    cursor: pointer;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb:hover {\r\n    background: #888;\r\n    cursor: pointer;\r\n  }\r\n\r\n  &::-webkit-scrollbar-corner {\r\n    background: #d8d9db;\r\n  }\r\n}\r\n\r\n.el-table::before {\r\n  left: 0;\r\n  bottom: 0;\r\n  width: 100%;\r\n  height: 0px;\r\n}\r\n\r\n.el-table__body-wrapper::-webkit-scrollbar {\r\n  height: 10px !important;\r\n  width: 10px !important;\r\n  cursor: pointer;\r\n}\r\n\r\n.el-table__body-wrapper::-webkit-scrollbar-track {\r\n  background: #f1f4fb;\r\n  border-radius: 2px;\r\n}\r\n\r\n.el-table__body-wrapper::-webkit-scrollbar-thumb {\r\n  background: #d8d9db;\r\n  border-radius: 10px;\r\n}\r\n\r\n.el-table__body-wrapper::-webkit-scrollbar-thumb:hover {\r\n  background: #c9c9ca;\r\n}\r\n\r\n.el-dialog {\r\n  border-radius: 8px !important;\r\n\r\n  .el-dialog__header {\r\n    // background: #0061ce;\r\n    background: #1768eb !important;\r\n    border-top-left-radius: 8px !important;\r\n    border-top-right-radius: 8px !important;\r\n    padding: 10px 20px !important;\r\n\r\n    .el-dialog__title {\r\n      // color: #fff\r\n      font-size: 16px !important;\r\n      font-family: PingFang SC, PingFang SC !important;\r\n      font-weight: bold !important;\r\n      color: #fff !important;\r\n    }\r\n\r\n    .el-dialog__close {\r\n      // color: #fff !important;\r\n      font-size: 20px !important;\r\n      font-weight: bold !important;\r\n      color: #fff !important;\r\n    }\r\n    .el-dialog__headerbtn {\r\n      top: 15px !important;\r\n    }\r\n  }\r\n\r\n  .el-dialog__body {\r\n    padding: 50px 20px;\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    // border-top: 1px solid #EBEBEB;\r\n    padding-top: 0px !important;\r\n    margin-top: 0 !important;\r\n  }\r\n}\r\n\r\n.el-table--striped .el-table__body tr.el-table__row--striped td {\r\n  background: #fafbfc;\r\n}\r\n\r\n.el-table--enable-row-hover .el-table__body tr:hover > td {\r\n  background: #f5f7fa;\r\n}\r\n\r\n.el-select-dropdown__item.hover,\r\n.el-select-dropdown__item:hover {\r\n  background: #f1f5ff;\r\n  color: #0581ff;\r\n}\r\n\r\n.el-table {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.el-table .cell {\r\n  white-space: pre-line;\r\n}\r\n\r\n.el-pagination {\r\n  text-align: right;\r\n}\r\n\r\n.el-pagination.is-background .el-pager li {\r\n  border: 1px solid #dfe1f1;\r\n  background: rgba(255, 255, 255, 1);\r\n  border-radius: 4px;\r\n}\r\n\r\n.el-pagination.is-background .btn-prev,\r\n.el-pagination.is-background .btn-next {\r\n  border: 1px solid #dfe1f1;\r\n  background: rgba(255, 255, 255, 1);\r\n}\r\n\r\n.el-button {\r\n  font-size: 14px !important;\r\n}\r\n\r\n.el-upload-list__item.is-success.focusing .el-icon-close-tip {\r\n  display: none !important;\r\n}\r\n\r\n.el-submenu,\r\n.el-menu-item {\r\n  font-size: 15px !important;\r\n  // min-width: 160px !important; //\r\n}\r\n\r\n.el-submenu__title {\r\n  font-size: 16px;\r\n}\r\n\r\n.el-breadcrumb__inner {\r\n  > a {\r\n    color: #999;\r\n  }\r\n}\r\n\r\n.el-popover--plain {\r\n  white-space: pre-line;\r\n}\r\n\r\nbody {\r\n  .el-button {\r\n    border-radius: 8px;\r\n  }\r\n}\r\n\r\n// .el-input{\r\n//   input{\r\n//     border-radius: 6px !important;\r\n//   }\r\n// }\r\n.el-radio-button--medium .el-radio-button__inner {\r\n  border: none !important;\r\n  border-radius: 6px !important;\r\n  background: rgba(0, 97, 206, 0.08);\r\n  color: #1f71cf;\r\n  font-size: 16px;\r\n  font-family: PingFang SC RE;\r\n  font-weight: bold;\r\n}\r\n\r\n// Tab、\r\n.el-tabs__item {\r\n  font-size: 16px !important;\r\n  font-family: PingFang SC, PingFang SC !important;\r\n  font-weight: 500 !important;\r\n  color: #81859e !important;\r\n  height: 60px !important;\r\n  line-height: 60px !important;\r\n}\r\n\r\n.el-tabs__item.is-active {\r\n  color: #1071e2 !important;\r\n  font-size: 16px !important;\r\n  font-family: PingFang SC, PingFang SC !important;\r\n  font-weight: bold !important;\r\n}\r\n\r\n.el-tabs__item:hover {\r\n  color: #1071e2 !important;\r\n  font-size: 16px !important;\r\n  font-family: PingFang SC, PingFang SC !important;\r\n  font-weight: bold !important;\r\n}\r\n\r\n///////////穿梭框\r\n::v-deep .el-checkbox__label {\r\n  font-size: 16px;\r\n  font-family: PingFang SC, PingFang SC;\r\n  font-weight: 500;\r\n  color: #202020;\r\n}\r\n\r\n// 列表（）\r\n.el-table::before {\r\n  left: 0;\r\n  bottom: 0;\r\n  width: 100%;\r\n  height: 0px !important;\r\n}\r\n\r\n// ----element-ui样式修改 -end\r\n\r\n.leaflet-container {\r\n  font-size: 12px !important;\r\n  path {\r\n    outline: none;\r\n  }\r\n  .leaflet-tooltip {\r\n    padding: 0;\r\n    border: none;\r\n  }\r\n}\r\n\r\n.truncate {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/styles/index.scss b/src/styles/index.scss
--- a/src/styles/index.scss	(revision 2dc6d68a6118a4994fdfcca9ac577843f012d219)
+++ b/src/styles/index.scss	(date 1756279783497)
@@ -436,21 +436,6 @@
   }
 }
 
-// .el-input{
-//   input{
-//     border-radius: 6px !important;
-//   }
-// }
-.el-radio-button--medium .el-radio-button__inner {
-  border: none !important;
-  border-radius: 6px !important;
-  background: rgba(0, 97, 206, 0.08);
-  color: #1f71cf;
-  font-size: 16px;
-  font-family: PingFang SC RE;
-  font-weight: bold;
-}
-
 // Tab、
 .el-tabs__item {
   font-size: 16px !important;
