<template>
  <div style="position:relative;">

    <div
      id="lineCharts"
      style="height: calc(100vh - 9.5rem); width: 100%;"
    />
    <div
      class="remark"
    >
      <!-- <div class="remark_item">
        <div class="dot dot1" />
        <div class="text">正常值</div>
      </div> -->
      <div class="remark_item">
        <div class="dot dot2" />
        <div class="text">超过阈值</div>
      </div>
    </div>
    <!-- 历史数据图表数据 -->
    <el-dialog
      v-if="dialogVisible"
      title="历史数据"
      :visible.sync="dialogVisible"
      width="fit-content"
      top="90px"
      append-to-body
      destroy-on-close
    >
      <DetailChart
        ref="chartRef"
        :data="formData"
      />
    </el-dialog>

  </div>
</template>
<script>
import * as echarts from 'echarts'
import { deepClone } from '@/utils'
import DetailChart from '@/views/aobo/curveView/modules/DetailChart'

export default {
  name: 'LineCharts',
  components: { DetailChart },
  props: {
    chartsSelected: {
      require: true,
      type: Object,
      default: () => {}
    },
    zoomStart: {
      require: true,
      type: Number,
      default: 0
    },
    zoomEnd: {
      require: true,
      type: Number,
      default: 100
    }
  },
  data() {
    return {
      zoomStartChild: 0,
      zoomEndChild: 100,
      dialogVisible: false,
      // 详情数据
      formData: null,

    }
  },
  created() {
    window.handle = this.handle
  },
  mounted() {
    window.addEventListener('resize', this.onResize)
    this.zoomStartChild = this.zoomStart
    this.zoomEndChild = this.zoomEnd
    this.$nextTick(() => {
      this.initcharts()
    })
  },
  destroyed() {
    window.removeEventListener('resize', this.onResize)
  },
  methods: {
    // 点击跳转
    handle(row) {
      this.formData = deepClone(row)
      this.dialogVisible = true
    },
    initcharts() {
      this.chart = echarts.init(document.getElementById('lineCharts'))
      this.chart.on('dataZoom', (e) => {
        console.log('data zoom')
        this.zoomStartChild = e.batch[0].start
        this.zoomEndChild = e.batch[0].end
        this.$emit('zoomChange', {
          zoomStart: e.batch[0].start,
          zoomEnd: e.batch[0].end
        })
      })
      this.chart.on('legendselectchanged', (params) => {
        console.log(params, 'legendselectchanged')
        this.$emit('legendselectchanged', {
          chartsSelected: params.selected
        })
      })
      this.chart.on('contextmenu', (params) => {
        params.event.event.preventDefault()
        console.log('contextmenu', params)
      })
    },
    updateOption(seriesData) {
      const colorList = [
        '#5470c6',
        '#91cc75',
        '#fac858',
        '#ee6666',
        '#73c0de',
        '#3ba272',
        '#fc8452',
        '#9a60b4',
        '#ea7ccc'
      ]
      const nameArray = seriesData.map((item) => item.name)
      const nameObj = {}
      nameArray.forEach((item) => {
        nameObj[item] = true
      })
      const option = {
        legend: {
          type: 'plain',
          top: 0,
          right: 200,
          itemGap: 50,
          itemWidth: 20,
          itemHeight: 7,
          icon: 'roundRect',
          selected: this.chartsSelected || nameObj
        },
        grid: {
          left: 80,
          top: 40,
          bottom: 25,
          right: 80,
          containLabel: true
        },
        // dataZoom: [{
        //   // filterMode: 'none',
        //   type: 'inside',
        //   start: this.zoomStartChild,
        //   end: this.zoomEndChild
        //   // realtime: false
        // }],
        xAxis: {
          name: '光缆位置',
          type: 'value',
          nameTextStyle: {
            color: '#9FA3AB',
            fontSize: 14
          },
          axisLine: {
            lineStyle: {
              color: '#8F98A0'
            }
          },
          axisTick: { length: 0 },
          axisLabel: {
            textStyle: {
              color: '#909090'
            },
            fontSize: 14,
            formatter: (value) => `${(value / 100).toFixed(2)}m`
          },
          splitLine: {
            show: false,
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        yAxis: {
          name: '温度：℃',
          type: 'value',
          axisLine: { show: false },
          axisLabel: {
            color: '#9FA3AB'
            // formatter: (value) => `${(value * 100).toFixed(1)}%`
          },
          nameTextStyle: {
            color: '#9FA3AB',
            fontSize: 14,
            align: 'center',
            padding: [0, 0, 5, 0]
          },
          // max(value) {
          //   return value.max + 2
          // },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed'
            }
          },
          splitNumber: 10
        },
        tooltip: {
          trigger: 'axis',
          triggerOn: 'click',
          enterable: true,
          padding: [12, 15, 20, 20],
          textStyle: { color: '#424242' },
          renderMode: 'html',
          formatter(params) {
            console.log(params, 'paramsparamsparamsparamsparamsparamsparamsparams')
            let html = ``
            params.forEach((v) => {
              html += `<div style="margin-bottom:0px;">
                  <div style="display:flex;align-items:center;margin-top:5px">
                    <div
                      style="
                        margin-right:10px;
                        border-radius:6px;
                        width:23px;height:7px;
                        background-color:${colorList[v.componentIndex]};
                      "
                    ></div>
                    <div style="margin-right:20px;margin-bottom:2px">
                    <span style="margin-right:15px;"> ${(v.data[2].position / 100).toFixed(2)}m</span>
                    <span>${`${(v.data[2].value / 100).toFixed(2)}℃`}</span>
                    <span style="color:#1768EB;cursor:pointer;margin-right:10px;" onclick='handle(${JSON.stringify(v.data[2])})' >历史数据</span>
                   </div>
                  </div>
                  <div style="display:flex;align-items:center;margin-bottom:5px">
                    <div style="margin-right:15px;">
                      <span>电缆位置：${((v.data[2].position / 100) + Number(v.data[2].startPosition) / 100).toFixed(2)}m</span>
                    </div>
                    <div>
                     光缆分段： ${v.data[2].paragraphStr || ''}
                     </div>
                  </div>
                </div>`
            })

            return html
          },
          className: 'tooltip'
        },
        dataZoom: [
          {
            type: 'inside',
            start: this.zoomStartChild,
            end: this.zoomEndChild,
            // zoomLock: false, // 允许缩放
          },
          {
            type: 'slider', // 滑动条单独显示
            show: true, // 是否显示滑动条
            start: this.zoomStartChild,
            end: this.zoomEndChild,
            height: 8, // 滑动条组件高度
            bottom: 5, // 距离图表区域下边的距离
            borderRadius: 5,
            showDetail: true, // 拖拽时显示详情
            showDataShadow: false,
            fillerColor: 'rgba(84, 112, 198, 0.6)', // 平移条的填充颜色
            borderColor: 'transparent', // 边框颜色rgb(204, 207, 215)
            backgroundColor: '#CCCFD7', // 背景颜色
            handleStyle: {
              color: '#019e59', // 手柄颜色
            },
            textStyle: {
              fontSize: 12,
            },
          },
        ],
        series: seriesData
      }
      this.chart.setOption(option)
    },
    onResize() {
      this.chart.resize()
    }
  }
  // ${`${v.data[2].areaStr || ''}-${v.data[2].paragraphStr || ''}-${v.data[2].position / 100}m：`}

  //   <span>${v.seriesName}-</span>
  //                   ${(() => {
  //   if (v.data[2].segmentStr) {
  //     return `<span>${v.data[2].segmentStr}</span>`
  //   }
  //   return `<span style="color: #b6b60d">未定义线段</span>`
  // })()}
}
</script>
<style lang="scss" scoped>
.remark {
  position: absolute;
  top: 0;
  right: 20px;
  display: flex;
  justify-content: space-between;
  width: 130px;
  height: 20px;
  // line-height: 50px;
  .remark_item {
    display: flex;
    align-items: center;
    .dot {
      // width: 21px;
      // height: 6px;
      // margin-right: 5px;
      // border-radius: 2px;
      width: 10px;
      height: 10px;
      margin-right: 5px;
      border-radius: 10px;
    }
    .dot1 {
      background-color: #00E667;
    }
    .dot2 {
      background-color: red;
    }
    .text {
      color: black;
      font-family: PingFang SC RE;
      // font-weight: bold;
      font-size: 14px;
    }
  }
}
</style>
