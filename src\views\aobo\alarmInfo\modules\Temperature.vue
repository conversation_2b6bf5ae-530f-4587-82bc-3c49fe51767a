<template>
  <div class="main-content">
    <div style="display:flex;">
      <div class="list_box">
        <div class="title">
          <div class="list_name">信息列表</div>
        </div>
        <div
          style="margin-top: 28px;overflow-y: auto;"
          :style="{maxHeight: innerHeight - 280 + 'px'}"
        >
          <el-tree
            ref="tree"
            :data="data"
            :props="treePops"
            :highlight-current="true"
            node-key="id"
            @node-click="handleNodeClick"
          >
            <div
              slot-scope="{ node, data }"
              class="custom-tree-node"
            >
              <span
                :style="{color:node.isCurrent ? '#0358CB' : ''}"
                style="font-weight:bold;"
              >{{ data.name }}</span>
            </div>
          </el-tree>
        </div>

      </div>
      <div style="width: 80%; margin-left: 20px">
        <div class="top">
          <div style="display:flex;">
            <el-date-picker
              v-model="date"
              type="daterange"
              style="margin-right:20px;width:300px;"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="getList(1)"
            />
            <el-select
              v-model="status"
              placeholder="状态"
              class="input_style"
              @change="getList(1)"
            >
              <el-option
                label="未处置"
                :value="0"
              />
              <el-option
                label="已处置"
                :value="1"
              />
            </el-select>
            <el-input
              v-model="keyword"
              suffix-icon="el-icon-search"
              class="input_style"
              placeholder="请输入搜索内容"
              @clear="getList(1)"
              @blur="getList(1)"
            />
            <div class="range-select">
              <div style="font-weight:bold;">光缆范围:</div>
              <el-input-number
                v-model="startPosition"
                :min="0"
                :controls="false"
                @change="rangeChange"
              />
              <span>~</span>
              <el-input-number
                v-model="endPosition"
                :min="startPosition"
                :controls="false"
                @change="rangeChange"
              />
              <span>米</span>
            </div>
          </div>
          <div>
            <el-button
              type="primary"
              plain
              @click="getList(1)"
            >查询
            </el-button>
            <el-button
              type="primary"
              plain
              @click="reset()"
            >重置
            </el-button>
          </div>
        </div>
        <div class="line" />
        <div
          class="content-table"
        >
          <div class="title">
            <div class="title_name">报警信息</div>
            <div>
              <el-button
                type="primary"
                :disabled="!btnAuthorityList.includes('batch-dispose')"
                @click="batchHandle"
              >批量处置
              </el-button>
              <el-button
                :loading="exporting"
                type="success"
                :disabled="!btnAuthorityList.includes('export')"
                @click="batchExport"
              >批量导出</el-button>
              <el-button
                type="danger"
                @click="batchDel"
              >批量删除
              </el-button>
            </div>
          </div>
          <el-table
            v-loading="loading"
            :header-cell-style="tableHeaderStyle"
            header-row-class-name="table-header"
            :data="tableData"
            stripe
            style="width: 100%"
            :style="{maxHeight: innerHeight - 380 + 'px'}"
            :height="innerHeight - 380"
            row-key="id"
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              type="selection"
              width="50"
            />
            <el-table-column
              label="序号"
              type="index"
              width="50"
              align="center"
            />
            <el-table-column
              prop="alarmTime"
              label="报警时间"
              show-overflow-tooltip
              align="center"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.alarmTime || '--' }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="tempVal"
              label="报警温度（℃）"
              align="center"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <span
                  :style="{color:(scope.row.tempVal || scope.row.tempVal=== 0) ? '#F85656' : '#333333'}"
                >{{
                  (scope.row.tempVal || scope.row.tempVal === 0) ?
                    `${(scope.row.tempVal / 100).toFixed(2)}℃` : '--'
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="current"
              label="电流（A）"
              align="center"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <span>{{ (scope.row.current || scope.row.current==0)?scope.row.current:'--' }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="tempVal"
              label="报警等级"
              align="center"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <span>{{ alarmLevelMap[scope.row.level] }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="thresholdVal"
              label="阈值"
              align="center"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <span>{{
                  (scope.row.thresholdVal || scope.row.thresholdVal === 0) ?
                    `${(scope.row.thresholdVal / 100).toFixed(2)}℃` : '--'
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              width="300"
              prop="tierCabinetStr"
              label="故障光缆位置"
              align="center"
              show-overflow-tooltip
            >
              <template slot-scope="{row}">
                <span>{{
                  (row.mineName ? row.mineName + '-' : '') +
                    (row.workFaceName ? row.workFaceName + '-' : '') +
                    (row.segmentName ? row.segmentName + '-' : '') +
                    ((row.position || row.position === 0) ? (row.position / 100) + 'm处' : '')
                }}</span>
              </template>
            </el-table-column>

            <!--            <el-table-column-->
            <!--              prop="pointStr"-->
            <!--              label="测温点"-->
            <!--              show-overflow-tooltip-->
            <!--              align="center"-->
            <!--            >-->
            <!--              <template slot-scope="scope">-->
            <!--                <span>{{ scope.row.pointStr || '&#45;&#45;' }}</span>-->
            <!--              </template>-->
            <!--            </el-table-column>-->

            <el-table-column
              prop="status"
              label="状态"
              align="center"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <span
                  :style="{color: scope.row.status === 0 ? '#FF9600' :
                    scope.row.status === 1 ? '#02C69E' : '#333333'}"
                >
                  {{
                    scope.row.status === 0 ? '未处置' :
                    scope.row.status === 1 ? '已处置' : '--'
                  }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="btnArrList.length"
              label="操作"
              fixed="right"
              align="center"
              width="100"
            >
              <template v-slot="scope">
                <div class="control">

                  <el-button
                    v-if="scope.row.status !== 1"
                    type="text"
                    :disabled="!btnAuthorityList.includes('dispose')"
                    style="text-decoration:underline;"
                    @click="handleClick('handle', scope.row)"
                  >处置
                  </el-button>
                  <el-button
                    v-else
                    type="text"
                    style="text-decoration:underline;"
                    @click="detail(scope.row)"
                  >查看
                  </el-button>

                  <el-button
                    type="text"
                    style="color:#F56C6C;text-decoration:underline;"
                    @click="del(scope.row)"
                  >删除
                  </el-button>

                </div>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            background
            :current-page.sync="pageNum"
            :page-size="pageSize"
            layout="total,prev, pager, next,sizes, jumper"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <!-- 报警处置 -->
    <el-dialog
      title="报警处置"
      :visible.sync="dialogVisible"
      width="450px"
      :modal-append-to-body="false"
      top="30vh"
      @close="closeDialog()"
    >
      <div class="handle-detail">
        <span class="label" style="margin-top:10px">处置人：</span>
        <el-select
          v-model="disposer"
          class="input"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.name"
            :value="item.name"
          />
        </el-select>
      </div>

      <div class="handle-detail">
        <span class="label">处置详情：</span>
        <el-input
          v-model="desc"
          class="input"
          type="textarea"
          resize="none"
        />
      </div>

      <div style="display:flex;align-items:center;padding-left:50px;margin-top: 12px;">
        <el-input-number
          v-model="alarmNum"
          controls-position="right"
          :min="1"
        />
        <span style="margin-left:10px;">分钟内不再次报警</span>
      </div>
      <div
        slot="footer"
        class="dialog_footer"
      >
        <el-button
          type="primary"
          plain
          style="margin-right:10px"
          @click="closeDialog()"
        >取 消
        </el-button>
        <el-button
          type="primary"
          @click="handleAlarmSubmit()"
        >确 定
        </el-button>
      </div>
    </el-dialog>
    <!--    处置详情-->
    <el-dialog
      title="处置详情"
      :visible.sync="detailVisible"
      width="450px"
      :modal-append-to-body="false"
      top="30vh"
      @close="closeDetail"
    >
      <el-form label-width="3.125rem">
        <el-form-item label="处置人：">
          {{ select.disposer }}
        </el-form-item>
        <el-form-item label="处置时间：">
          {{ select.disposerTime }}
        </el-form-item>
        <el-form-item label="处置详情：">
          {{ select.disposerDesc }}
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 删除 -->
    <el-dialog
      title="删除"
      :visible.sync="dialogVisibleDel"
      :modal-append-to-body="false"
      width="500px"
      top="320px"
      @close="closeDelDialog()"
    >
      <div style="display:flex;align-items:center;padding-left:50px">
        <img
          src="@/assets/<EMAIL>"
          style="width:20px;height:20px;margin-right:10px"
        >
        <div style="font-size:16px;color:#F94E4E">
          确认删除所选数据吗？
        </div>
      </div>
      <div
        slot="footer"
        class="dialog_footer"
      >
        <el-button @click="closeDelDialog()">取 消</el-button>
        <el-button
          type="primary"
          @click="handleDel()"
        >确 认
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import {
  areaDropdown,
  doPage,
  del,
  disposeAlarm,
  batchExport, getAlarmTree
} from '@/api/sceneView'
import { deepClone, utf8to16 } from '@/utils'
import { dropdownList as userPage } from '@/api/aobo/userManagement'
import { alarmLevelMap } from '@/constants'
import dayjs from 'dayjs'
// import commonBtn from '@/components/commonBtn/index'

export default {
  name: 'AlarmInfo',
  components: {
    // commonBtn
  },
  data() {
    return {
      treePops: {
        label: 'spaceName',
        children: 'childList'
      },
      data: [{
        name: '全部',
        spaceType: 0,
        id: -1,
        serialNum: null,
        childList: []
      }],
      alarmLevelMap,
      parentSerialNum: null,
      btnAuthorityList: [], // 按钮权限
      date: [],
      status: null, // 状态
      position: null, // 煤矿位置
      keyword: '', // 煤矿位置
      startPosition: 0,
      endPosition: 5000,
      btnArrList: [
        {
          type: 'list',
          name: '处置',
          tagName: 'handle',
          color: '#1071E2'
        }
      ],
      operationList: ['handle'],
      tableData: [], // 列表数据
      loading: false,
      pageNum: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      alarmNum: 1, // 间隔时间
      select: {}, // 选中数据
      selectList: {}, // 选中数据
      // dropdownList: [], // 煤矿下拉框
      innerHeight: null,
      detailData: null, // 详情数据
      detailVisible: false,
      dialogVisibleDel: false,
      desc: null, // 处置详情
      userList: [], // 用户列表
      disposer: null, // 处置人
      exporting: false

    }
  },
  computed: {
    ...mapGetters([
      'tableHeaderStyle', 'btnAuthority'
    ])
  },
  created() {
    const cur = this.btnAuthority.find((item) => item.code === this.$route.meta.code)
    if (cur) this.btnAuthorityList = cur.functionPermissionCode
    this.getUserList()
    const endDate = new Date()
    const startDate = new Date()
    startDate.setMonth(startDate.getMonth() - 1)
    this.date = [dayjs(startDate).format('YYYY-MM-DD'), dayjs(endDate).format('YYYY-MM-DD')]
  },
  mounted() {
    this.innerHeight = window.innerHeight
    window.onresize = () => {
      this.innerHeight = window.innerHeight
    }
    this.getAlarmTree()
    // this.areaDropdown()
    this.getList(1)
  },
  methods: {
    // 设置树名称
    setTreeName(arr) {
      arr.forEach((item) => {
        if (item.spaceName && !item.name) {
          this.$set(item, 'name', item.spaceName)
        }
        if (item.childList && item.childList.length) this.setTreeName(item.childList)
      })
    },

    /**
     * 获取用户列表
     * */
    getUserList() {
      userPage()
        .then((res) => {
          this.userList = res.data
        })
    },

    getAlarmTree() {
      getAlarmTree().then((res) => {
        this.data[0].childList = res.data
        this.setTreeName(this.data[0].childList)
        this.$refs.tree.setCurrentKey(-1)
      })
    },
    handleNodeClick(data) {
      // switch (data.spaceType) {
      //   case 0:
      //     console.log('全部')
      //     break
      //   case 1:
      //     console.log('1')
      //     break
      //   case 2:
      //     console.log('2')
      //     break
      //   case 3:
      //     console.log('3')
      //     break
      //   case 4:
      //     console.log('4')
      //     break
      //   default:
      //     console.log('default')
      //     break
      // }
      this.parentSerialNum = data.serialNum
      this.getList(1)
    },
    // // 区域下拉框
    // areaDropdown() {
    //   areaDropdown().then((res) => {
    //     this.dropdownList = res.data
    //   })
    // },
    // 重置
    reset() {
      this.date = []
      this.status = null
      this.position = null
      this.keyword = ''
      this.getList(1)
    },
    getList(isPage) {
      if (isPage) {
        this.pageNum = 1
        this.selectList = []
      }
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        query: {
          spaceId: this.position,
          status: this.status,
          startDate: this.date && this.date.length ? this.date[0] : '',
          endDate: this.date && this.date.length ? this.date[1] : '',
          parentSerialNum: this.parentSerialNum,
          startPosition: this.startPosition * 100,
          endPosition: this.endPosition * 100
        },
        keyword: this.keyword
      }
      this.loading = true
      doPage(params).then((res) => {
        this.tableData = res.data.records.map((item) => item)
        this.pageNum = res.data.current
        this.total = Number(res.data.total)
      }).finally(() => {
        this.loading = false
      })
    },
    // 改变每页条数
    handleSizeChange(val) {
      this.pageSize = val
      this.getList()
    },
    // 分页
    handleCurrentChange(val) {
      this.pageNum = val
      this.getList()
    },
    // 多选改变
    handleSelectionChange(list) {
      this.selectList = deepClone(list)
    },
    // 批量处置
    batchHandle() {
      if (!this.selectList.length) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      const obj = this.selectList.find((item) => item.status === 1)
      if (obj) {
        this.$message.warning('所选数据中含有已处置数据，请重新选择')
        return
      }
      this.dialogVisible = true
    },
    // 报警处置
    handleAlarm(e) {
      this.dialogVisible = true
      this.select = e
    },
    // 报警处置提交
    handleAlarmSubmit() {
      if (!this.alarmNum) {
        this.$message.warning('请输入时间')
        return
      }
      const data = {
        intervalMinute: this.alarmNum,
        desc: this.desc,
        disposer: this.disposer,
        alarmIds: [this.select.id]
      }

      if (this.select && this.select.id) {
        data.alarmIds = [this.select.id]
      } else {
        data.alarmIds = this.selectList.map((item) => item.id)
      }
      disposeAlarm(data).then((res) => {
        this.$message.success('成功')
        this.getList(1)
        this.closeDialog()
      })
    },
    closeDialog() {
      this.alarmNum = 1
      this.dialogVisible = false
      this.select = {}
      this.disposer = null
      this.desc = null
    },
    // 批量导出
    batchExport() {
      const data = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        query: {
          spaceId: this.position,
          status: this.status,
          startDate: this.date && this.date.length ? this.date[0] : '',
          endDate: this.date && this.date.length ? this.date[1] : '',
          parentSerialNum: this.parentSerialNum,
          startPosition: this.startPosition * 100,
          endPosition: this.endPosition * 100
        },
        keyword: this.keyword
      }
      this.exporting = true
      batchExport(data).then((res) => {
        const url = window.URL.createObjectURL(
          new Blob([res.data], {
            type: 'application/vnd.ms-excel;charset=UTF-8'
          })
        )
        const temp = res.headers['content-disposition']
          .split(';')[1]
          .split('filename=')[1]
        const index = temp.indexOf('.')
        const str = temp.substr(0, index)
        const fileName = `${utf8to16(unescape(str))}.xlsx`
        const link = document.createElement('a')
        link.href = url
        link.download = fileName
        link.click()
        this.$message({
          type: 'success',
          message: '下载成功'
        })
      }).finally(() => {
        this.exporting = false
      })
    },
    // 按钮点击事件
    handleClick(tagName, e) {
      switch (tagName) {
        case 'handle':
          this.handleAlarm(e)
          break
        default:
          break
      }
    },
    detail(row) {
      this.select = deepClone(row)
      this.detailVisible = true
    },
    // 删除
    del(e) {
      this.select = deepClone(e)
      this.dialogVisibleDel = true
    },
    // 批量删除
    batchDel() {
      if (!this.selectList.length) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      this.dialogVisibleDel = true
    },

    // 删除取消
    closeDelDialog() {
      this.select = {}
      this.dialogVisibleDel = false
    },
    // 批量删除
    handleDel() {
      const data = (this.select && this.select.id) ? [this.select.id] : this.selectList.map((item) => item.id)
      del(data).then((res) => {
        if (res.code === 200) {
          this.$message.success('成功')
          if ((this.select && this.select.id) && (this.pageNum > 1) && this.tableData.length === 1) {
            this.pageNum--
          } else if ((this.pageNum > 1) && this.tableData.length === this.selectList.length) {
            this.pageNum--
          }
          this.getList()
          this.closeDelDialog()
        }
      })
    },
    closeDetail() {
      this.select = {}
      this.detailVisible = false
    },
    rangeChange() {
      if (this.endPosition <= this.startPosition) {
        this.$message.error('请输入正确的范围')
      }
    }

  }

}
</script>

<style lang="scss" scoped>
.main-content {
  box-sizing: border-box;
  font-family: PingFang SC RE;

  .top {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    .input_style {
      margin-right: 20px;
      width: 200px;
    }
  }

  .line {
    width: 100%;
    height: 1px;
    background: #E5E5E5;
    margin-bottom: 50px;
  }

  .content-table {
    width: 100%;
    position: relative;

    .title {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;

      .title_name {
        height: 20px;
        font-size: 18px;
        font-family: PingFang SC RE;
        font-weight: bold;
        color: #202225;
        padding-left: 10px;
        border-left: 4px solid #1768EB;
      }
    }
  }

  .dialog_footer {
    display: flex;
    justify-content: center;
    // margin-top: 20px;
  }
}
.list_box {
  width: 16%;
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #E0E0E0;
}
.list_name {
  margin-top: 30px;
  height: 20px;
  font-size: 18px;
  font-family: PingFang SC RE;
  font-weight: bold;
  color: #202225;
  padding-left: 10px;
  border-left: 4px solid #1768EB;
}
.control {
  display: flex;
}

.handle-detail {
  display: flex;
  margin-top: 10px;
  padding-left: 50px;
  .label {
    flex-shrink: 0;
    width: 80px;
    text-align: start;
  }
  .input {
    width: 250px;
  }
}

.range-select {
  display: flex;
  align-items: center;
  gap: 0 10px;
  margin-left: 30px;
  .el-input-number {
    width: 70px;
  }
}

</style>
