<template>
  <div>
    <el-radio-group v-model="currentTab" @change="changeTab">
      <el-radio-button :label="1">截面分析</el-radio-button>
      <el-radio-button :label="2">曲线分析</el-radio-button>
    </el-radio-group>
    <span style="margin-left: 20px;">
      <span>当前数据采集时间：</span>
      <span>{{ refreshTime }}</span>
    </span>

    <div style="margin-top: 12px;">
      <HeatMapChart v-if="currentTab === 1" :temp-level-list="tempLevelList" :data="data" />
      <div v-else>
        <LineCharts v-if="showChart" ref="chartRef" v-loading="loading" />

        <div
          v-else
          class="no_data"
          style="height: 500px"
        >
          <img
            src="@/assets/page/yypz_icon_zanwu.png"
          >
          <div>暂无数据</div>
        </div>

      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import LineCharts from '@/views/aobo/curveView/modules/lineCharts.vue'
import { liveTrend } from '@/api/lineCharts'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import HeatMapChart from './HeatMapChart.vue'

const alarmColorMap = {
  0: '#0000FF',
  1: '#FFA500',
  2: '#e4e46f',
  3: '#FF0000',
}

export default Vue.extend({
  name: 'MarkerDetail',
  components: { HeatMapChart, LineCharts },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    tempLevelList: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      loading: true,
      colorList: [
        { color1: '#5470c6', color2: 'rgba(84, 112, 198, 0)' },
        { color1: '#91cc75', color2: 'rgba(145, 204, 117, 0)' },
        { color1: '#fac858', color2: 'rgba(250, 200, 88, 0)' },
        { color1: '#ee6666', color2: 'rgba(238, 102, 102, 0)' },
        { color1: '#73c0de', color2: 'rgba(115, 192, 222, 0)' },
        { color1: '#3ba272', color2: 'rgba(59, 162, 114, 0)' },
        { color1: '#fc8452', color2: 'rgba(252, 132, 82, 0)' },
        { color1: '#9a60b4', color2: 'rgba(154, 96, 180, 0)' },
        { color1: '#ea7ccc', color2: 'rgba(234, 124, 204, 0)' },
      ],
      currentTab: 1,
      showChart: true,
      refreshTime: null,

    }
  },
  created() {
    this.changeTab(this.currentTab)
  },

  methods: {

    /**
     * 根据温度判断报警等级
     * */
    getAlarmLevel(temp) {
      if (!this.tempLevelList.length) return
      for (const item of this.tempLevelList) {
        if (temp >= item.value) {
          return item.level
        }
      }
    },

    getChartsData() {
      const { parentData } = this.data
      const data = {
        segmentIds: [parentData.id],
        // min: parentData.startPosition,
        // max: parentData.endPosition
      }
      this.loading = true
      // this.$refs.chartRef?.chart?.clear()
      liveTrend(data).then((res) => {
        const chartsData = {
          xAxisData: [],
          seriesData: []
        }
        let lineChartData = []
        if (!res.data || !res.data.segments.length) {
          return
        }
        const { segments } = res.data

        lineChartData = segments
        // 判断哪一项有数据,如果都没有temperatures数据，则为暂无数据
        const haveDataArray = lineChartData.find((item) => (item.temperatures || []).length)
        if (haveDataArray) {
          chartsData.xAxisData = haveDataArray.temperatures.map((item) => item.position)
          this.showChart = true
        } else {
          this.showChart = false
          return
        }
        lineChartData.forEach((item) => {
          item.temperatures.forEach((item1) => {
            item1.fiberId = item.fiberId
            item1.startPosition = item.startPosition
          })
        })

        chartsData.seriesData = lineChartData.map((item, index) => {
          const datas = (item.temperatures || []).map((item1) => {
            item1.value = item1.temperature
            const alarmLevel = this.getAlarmLevel(item1.value)
            console.log(item1.value, alarmLevel, 'alarmLevel')
            this.$set(item1, 'itemStyle', {
              color: alarmColorMap[alarmLevel] || '#00E667'
            })
            this.$set(item1, 'itemStyle', {
              color: alarmColorMap[alarmLevel] || '#00E667',
              opacity: alarmLevel !== undefined ? 1 : 0
            })
            return [item1.position, (item1.value / 100).toFixed(2), item1]
          })
          const that = this
          return {
            name: `${item.name}`,
            type: 'line',
            data: datas,
            smooth: true, // 平滑曲线
            sampling: 'average',
            large: true,
            symbolSize(params) {
              return that.getAlarmLevel(params[1]) !== undefined ? 8 : 0
            },
            itemStyle: {
              normal: {
                color(params) {
                  // params 是当前数据点的信息
                  return alarmColorMap[that.getAlarmLevel(params.value[1])] || that.colorList[index].color1
                }
              }
            },
            lineStyle: { width: 2.5, color: this.colorList[index].color1 },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: this.colorList[index].color2 },
                { offset: 0.9, color: '#fff' }
              ])
            },
            label: {
              show: false,
              position: 'top',
              formatter: (val) => `${(val.value / 100).toFixed(2)}℃`
            }
          }
        })
        setTimeout(() => {
          this.$refs.chartRef?.updateOption(chartsData.seriesData)
        })
      }).finally(() => {
        this.loading = false
      })
    },

    changeTab(tab) {
      switch (tab) {
        case 1:
          break
        case 2:
          this.getChartsData()
          break
      }
      this.refreshTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
    }
  },

})
</script>

<style scoped lang="scss">

</style>
